#!/usr/bin/env python3
"""
HTTP Notification System for File Monitor Service.

This module provides HTTP notification functionality for sending file change
events to remote endpoints with comprehensive debug logging and error handling.
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Optional

# External dependencies
try:
    import httpx
    from httpx import TimeoutException, ReadTimeout, ConnectError, NetworkError
except ImportError as e:
    print(f"❌ Missing required dependency: {e}")
    print("Install with: pip install httpx")
    import sys
    sys.exit(1)

# Local imports
from logging_utils import LOGGER
from utils import EventType, FileChangeEvent, MonitorConfig, normalize_path_for_http


class HTTPNotifier:
    """Handles HTTP notifications for file changes with comprehensive debug logging."""
    
    def __init__(self, config: MonitorConfig):
        self.config = config
        self.client: Optional[httpx.AsyncClient] = None
        self.notification_stats = {
            'sent': 0,
            'failed': 0,
            'retries': 0
        }
    
    async def start(self):
        """Initialize HTTP client with debug logging."""
        timeout_config = httpx.Timeout(self.config.notification_timeout)
        
        LOGGER.debug(f"🔧 Initializing HTTP client with configuration:")
        LOGGER.debug(f"🔧 Timeout: {self.config.notification_timeout}s")
        LOGGER.debug(f"🔧 Retries: {self.config.notification_retries}")
        LOGGER.debug(f"🔧 Endpoints: {json.dumps(self.config.notification_urls, indent=2)}")
        
        self.client = httpx.AsyncClient(
            timeout=timeout_config,
            follow_redirects=True,
            verify=True
        )
        
        LOGGER.info(f"HTTP notifier initialized with endpoints: {list(self.config.notification_urls.values())}")
        LOGGER.debug(f"✅ HTTP client created successfully")
        LOGGER.debug(f"✅ Client timeout configuration: connect={timeout_config.connect}, read={timeout_config.read}, write={timeout_config.write}, pool={timeout_config.pool}")
    
    async def stop(self):
        """Close HTTP client with debug logging."""
        if self.client:
            LOGGER.debug("🔧 Closing HTTP client...")
            try:
                await self.client.aclose()
                LOGGER.debug("✅ HTTP client closed successfully")
            except Exception as e:
                LOGGER.error(f"❌ Error closing HTTP client: {e}")
            finally:
                self.client = None
        else:
            LOGGER.debug("🔧 HTTP client was not initialized")
            
        LOGGER.info("HTTP notifier stopped")
        LOGGER.debug(f"📊 Final notification stats: {self.get_stats()}")
    
    def _get_notification_url(self, event_type: EventType) -> str:
        """Get the appropriate notification URL based on event type."""
        if event_type == EventType.DELETED:
            return self.config.notification_urls.get("file_delete", self.config.notification_urls["file_update"])
        elif event_type == EventType.FILE_ADD:
            return self.config.notification_urls.get("file_add", self.config.notification_urls["file_update"])
        elif event_type == EventType.FILE_MOVE:
            return self.config.notification_urls.get("file_move", self.config.notification_urls["file_update"])
        else:
            # For CREATED, MODIFIED, and MOVED events, use file_update endpoint
            return self.config.notification_urls["file_update"]

    async def send_notification(self, event: FileChangeEvent) -> bool:
        """Send HTTP notification for file change event with comprehensive debug logging."""
        if not self.client:
            LOGGER.error("HTTP client not initialized")
            return False

        # Get the appropriate URL based on event type
        notification_url = self._get_notification_url(event.event_type)

        # Prepare payload with enhanced information and normalized paths
        payload = {
            "file_path": normalize_path_for_http(event.file_path),
            "event_type": event.event_type.value,
            "timestamp": event.timestamp,
            "file_size": event.file_size,
            "file_extension": event.file_extension,
            "relative_path": normalize_path_for_http(event.relative_path) if event.relative_path else None,
            "dest_path": normalize_path_for_http(event.dest_path) if event.dest_path else None
        }

        # Add enhanced metadata for new event types
        if event.event_type == EventType.FILE_ADD:
            payload.update({
                "creation_time": event.creation_time,
                "parent_directory": event.parent_directory
            })
        elif event.event_type == EventType.FILE_MOVE:
            payload.update({
                "previous_file_path": event.previous_file_path,
                "new_file_path": event.new_file_path
            })

        # Remove None values from payload
        payload = {k: v for k, v in payload.items() if v is not None}

        # Prepare headers
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "FileMonitor/1.0",
            "X-Event-Type": event.event_type.value,
            "X-Timestamp": str(event.timestamp)
        }

        LOGGER.debug(f"🚀 Starting HTTP notification process for {event.event_type.value} event")
        LOGGER.debug(f"📁 File: {event.file_path}")
        LOGGER.debug(f"🎯 Target URL: {notification_url}")
        LOGGER.debug(f"📦 Payload: {json.dumps(payload, indent=2)}")
        LOGGER.debug(f"📋 Headers: {json.dumps(headers, indent=2)}")

        notification_start_time = time.time()

        for attempt in range(self.config.notification_retries + 1):
            attempt_start_time = time.time()
            
            LOGGER.debug(f"🔄 Attempt {attempt + 1}/{self.config.notification_retries + 1} for {event.file_path}")
            
            try:
                # Log request details
                LOGGER.debug(f"📤 Sending POST request to {notification_url}")
                LOGGER.debug(f"📤 Request method: POST")
                LOGGER.debug(f"📤 Request headers: {json.dumps(dict(headers), indent=2)}")
                LOGGER.debug(f"📤 Request body: {json.dumps(payload, indent=2)}")
                
                response = await self.client.post(
                    notification_url,
                    json=payload,
                    headers=headers
                )

                attempt_duration = time.time() - attempt_start_time
                
                # Log response details
                LOGGER.debug(f"📥 Response received in {attempt_duration:.3f}s")
                LOGGER.debug(f"📥 Response status: {response.status_code}")
                LOGGER.debug(f"📥 Response headers: {json.dumps(dict(response.headers), indent=2)}")
                
                try:
                    response_text = response.text
                    LOGGER.debug(f"📥 Response body: {response_text}")
                    
                    # Try to parse as JSON for better formatting
                    try:
                        response_json = response.json()
                        LOGGER.debug(f"📥 Response JSON: {json.dumps(response_json, indent=2)}")
                    except:
                        pass  # Not JSON, already logged as text
                        
                except Exception as e:
                    LOGGER.debug(f"📥 Could not read response body: {e}")

                if response.status_code == 200:
                    total_duration = time.time() - notification_start_time
                    self.notification_stats['sent'] += 1
                    LOGGER.info(f"✅ Notification sent successfully to {notification_url}")
                    LOGGER.debug(f"✅ File: {event.file_path} ({event.event_type.value})")
                    LOGGER.debug(f"✅ Total duration: {total_duration:.3f}s")
                    LOGGER.debug(f"✅ Attempt: {attempt + 1}/{self.config.notification_retries + 1}")
                    return True
                else:
                    LOGGER.warning(f"⚠️ HTTP {response.status_code} response from {notification_url}")
                    LOGGER.warning(f"⚠️ File: {event.file_path}")
                    LOGGER.warning(f"⚠️ Response body: {response.text}")
                    
                    if response.status_code >= 400:
                        LOGGER.error(f"❌ HTTP Error {response.status_code}: {response.reason_phrase if hasattr(response, 'reason_phrase') else 'Unknown error'}")

            except (asyncio.TimeoutError, TimeoutException, ReadTimeout) as e:
                attempt_duration = time.time() - attempt_start_time
                LOGGER.warning(f"⏰ Timeout error (attempt {attempt + 1}) after {attempt_duration:.3f}s")
                LOGGER.warning(f"⏰ URL: {notification_url}")
                LOGGER.warning(f"⏰ File: {event.file_path}")
                LOGGER.debug(f"⏰ Timeout details: {type(e).__name__}: {e}")
                if attempt == 0:  # Only show this message on first attempt
                    LOGGER.info(f"💡 Note: Request to {notification_url} timed out after {self.config.notification_timeout}s")

            except (ConnectionError, ConnectError, NetworkError) as e:
                attempt_duration = time.time() - attempt_start_time
                LOGGER.warning(f"🔌 Connection error (attempt {attempt + 1}) after {attempt_duration:.3f}s")
                LOGGER.warning(f"🔌 URL: {notification_url}")
                LOGGER.warning(f"🔌 File: {event.file_path}")
                LOGGER.debug(f"🔌 Connection details: {type(e).__name__}: {e}")
                if attempt == 0:  # Only show this message on first attempt
                    LOGGER.info(f"💡 Note: Notification endpoint {notification_url} appears to be unavailable")

            except json.JSONDecodeError as e:
                attempt_duration = time.time() - attempt_start_time
                LOGGER.error(f"📝 JSON decoding error (attempt {attempt + 1}) after {attempt_duration:.3f}s")
                LOGGER.error(f"📝 Response that failed to decode: {response.text if 'response' in locals() else 'N/A'}")
                LOGGER.error(f"📝 JSON error details: {e}")
                
            except Exception as e:
                attempt_duration = time.time() - attempt_start_time
                LOGGER.error(f"❌ Unexpected error (attempt {attempt + 1}) after {attempt_duration:.3f}s")
                LOGGER.error(f"❌ URL: {notification_url}")
                LOGGER.error(f"❌ File: {event.file_path}")
                LOGGER.error(f"❌ Error type: {type(e).__name__}")
                LOGGER.error(f"❌ Error details: {e}")

            if attempt < self.config.notification_retries:
                self.notification_stats['retries'] += 1
                backoff_time = 1.0 * (attempt + 1)  # Exponential backoff
                LOGGER.debug(f"⏳ Waiting {backoff_time}s before retry...")
                await asyncio.sleep(backoff_time)

        total_duration = time.time() - notification_start_time
        self.notification_stats['failed'] += 1
        LOGGER.error(f"❌ Failed to send notification after {self.config.notification_retries + 1} attempts")
        LOGGER.error(f"❌ File: {event.file_path}")
        LOGGER.error(f"❌ URL: {notification_url}")
        LOGGER.error(f"❌ Total time elapsed: {total_duration:.3f}s")
        LOGGER.error(f"❌ Event type: {event.event_type.value}")
        return False
    
    def get_stats(self) -> Dict[str, int]:
        """Get notification statistics."""
        return self.notification_stats.copy()
    
    def log_stats(self) -> None:
        """Log detailed notification statistics."""
        stats = self.get_stats()
        total_attempts = stats['sent'] + stats['failed']
        success_rate = (stats['sent'] / total_attempts * 100) if total_attempts > 0 else 0
        
        LOGGER.debug(f"📊 HTTP Notification Statistics:")
        LOGGER.debug(f"📊 Total notifications sent: {stats['sent']}")
        LOGGER.debug(f"📊 Total notifications failed: {stats['failed']}")
        LOGGER.debug(f"📊 Total retry attempts: {stats['retries']}")
        LOGGER.debug(f"📊 Success rate: {success_rate:.1f}%")
        LOGGER.debug(f"📊 Total attempts: {total_attempts}")
        
        if total_attempts > 0:
            LOGGER.info(f"📊 Notification success rate: {success_rate:.1f}% ({stats['sent']}/{total_attempts})")
