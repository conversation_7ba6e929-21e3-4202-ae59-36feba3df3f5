#!/usr/bin/env python3
"""
Logging utilities for the File Monitor Service.

This module provides logging functionality including the FileMonitorLogger class
and logging setup utilities.
"""

import logging
import sys
from typing import Optional


class FileMonitorLogger:
    """Simple logger for file monitor service."""
    
    def __init__(self, name: str = "file_monitor", level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # Prevent duplicate handlers
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """Set up console and file handlers."""
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(console_handler)
    
    def add_file_handler(self, log_file: str, level: str = "DEBUG"):
        """Add file handler for logging to file."""
        try:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(getattr(logging, level.upper()))

            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(formatter)

            self.logger.addHandler(file_handler)
            self.info(f"Added file logging to: {log_file}")
        except Exception as e:
            self.error(f"Failed to add file handler: {e}")
    
    def set_level(self, level: str):
        """Set logging level."""
        self.logger.setLevel(getattr(logging, level.upper()))
    
    def debug(self, message: str):
        """Log debug message."""
        self.logger.debug(message)
    
    def info(self, message: str):
        """Log info message."""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Log warning message."""
        self.logger.warning(message)
    
    def error(self, message: str):
        """Log error message."""
        self.logger.error(message)
    
    def critical(self, message: str):
        """Log critical message."""
        self.logger.critical(message)


# Global logger instance
LOGGER = FileMonitorLogger()


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """Setup logging configuration."""
    LOGGER.set_level(log_level)
    
    if log_file:
        LOGGER.add_file_handler(log_file)
    
    LOGGER.info("Logging initialized")
