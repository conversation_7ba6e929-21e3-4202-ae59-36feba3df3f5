#!/usr/bin/env python3
"""
Core File Monitoring System for File Monitor Service.

This module provides the core file system monitoring functionality including
file event handling and the main monitoring service coordination.
"""

import asyncio
import fnmatch
import time
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any

# External dependencies
try:
    from watchdog.observers import Observer
    from watchdog.events import (
        FileSystemEventHandler, FileSystemEvent,
        FileCreatedEvent, FileModifiedEvent, FileDeletedEvent, FileMovedEvent
    )
except ImportError as e:
    print(f"❌ Missing required dependency: {e}")
    print("Install with: pip install watchdog")
    import sys
    sys.exit(1)

# Local imports
from logging_utils import LOGGER, setup_logging
from utils import EventType, FileChangeEvent, MonitorConfig
from http_notifier import HTTPNotifier


class FileEventHandler(FileSystemEventHandler):
    """Handles file system events with filtering and debouncing."""

    def __init__(self, config: MonitorConfig, event_callback: Callable[[FileChangeEvent], None]):
        super().__init__()
        self.config = config
        self.event_callback = event_callback
        self._pending_events: Dict[str, FileChangeEvent] = {}
        self._debounce_task: Optional[asyncio.Future] = None
        self._loop: Optional[asyncio.AbstractEventLoop] = None

        # Statistics
        self.stats = {
            'events_received': 0,
            'events_filtered': 0,
            'events_processed': 0
        }

    def set_event_loop(self, loop: asyncio.AbstractEventLoop):
        """Set the event loop for async operations."""
        self._loop = loop

    def _should_process_file(self, file_path: str) -> bool:
        """Check if file should be processed based on filters."""
        path_obj = Path(file_path)

        # Check if it's a directory
        if path_obj.is_dir():
            return False

        # Check file size
        try:
            if path_obj.exists() and path_obj.stat().st_size > self.config.max_file_size:
                return False
        except (OSError, IOError):
            pass

        # Check extension filters
        file_ext = path_obj.suffix.lower()

        if self.config.include_extensions:
            if file_ext not in self.config.include_extensions:
                return False

        if self.config.exclude_extensions:
            if file_ext in self.config.exclude_extensions:
                return False

        # Check directory exclusions
        for exclude_dir in self.config.exclude_directories:
            if exclude_dir in path_obj.parts:
                return False

        # Check pattern exclusions
        for pattern in self.config.exclude_patterns:
            if fnmatch.fnmatch(path_obj.name, pattern):
                return False

        return True

    def _create_event(self, event_type: EventType, src_path: str, dest_path: str = None) -> FileChangeEvent:
        """Create a FileChangeEvent from file system event."""
        # Calculate relative path
        relative_path = None
        for monitored_path in self.config.monitored_paths:
            try:
                relative_path = str(Path(src_path).relative_to(Path(monitored_path)))
                break
            except ValueError:
                continue

        return FileChangeEvent(
            event_type=event_type,
            file_path=src_path,
            dest_path=dest_path,
            relative_path=relative_path
        )

    def _schedule_debounced_event(self, event: FileChangeEvent):
        """Schedule event processing with debouncing."""
        LOGGER.debug(f"🔍 Scheduling debounced event for {event.file_path}, event_type: {event.event_type.value}")

        if not self._loop:
            LOGGER.debug(f"🔍 No event loop available, skipping event: {event.file_path}")
            return

        # Store the event (overwrites previous events for same file)
        self._pending_events[event.file_path] = event
        LOGGER.debug(f"🔍 Stored event in pending_events, total pending: {len(self._pending_events)}")

        # Cancel existing debounce task
        if self._debounce_task and not self._debounce_task.done():
            LOGGER.debug(f"🔍 Cancelling existing debounce task")
            self._debounce_task.cancel()

        # Schedule new debounce task using run_coroutine_threadsafe for cross-thread safety
        LOGGER.debug(f"🔍 Creating new debounce task using run_coroutine_threadsafe")
        future = asyncio.run_coroutine_threadsafe(self._process_debounced_events(), self._loop)
        # Store the future so we can cancel it if needed
        self._debounce_task = future

    async def _process_debounced_events(self):
        """Process pending events after debounce interval."""
        LOGGER.debug(f"🔍 Starting debounce wait for {self.config.debounce_interval}s")
        await asyncio.sleep(self.config.debounce_interval)

        # Process all pending events
        events_to_process = list(self._pending_events.values())
        self._pending_events.clear()

        LOGGER.debug(f"🔍 Processing {len(events_to_process)} debounced events")

        for event in events_to_process:
            try:
                LOGGER.debug(f"🔍 Calling event_callback for {event.file_path}")
                self.event_callback(event)
                self.stats['events_processed'] += 1
                LOGGER.debug(f"🔍 Event processed successfully: {event.file_path}")
            except Exception as e:
                LOGGER.error(f"Error processing event {event.file_path}: {e}")

    def on_created(self, event: FileSystemEvent):
        """Handle file creation events."""
        LOGGER.debug(f"🔍 on_created called for: {event.src_path}, is_directory: {event.is_directory}")

        if event.is_directory:
            LOGGER.debug(f"🔍 Skipping directory: {event.src_path}")
            return

        self.stats['events_received'] += 1
        LOGGER.debug(f"🔍 File creation event received: {event.src_path}")

        if not self._should_process_file(event.src_path):
            self.stats['events_filtered'] += 1
            LOGGER.debug(f"🔍 File filtered out: {event.src_path}")
            return

        LOGGER.debug(f"🔍 Processing file creation: {event.src_path}")

        # Emit both CREATED (for backward compatibility) and FILE_ADD events
        created_event = self._create_event(EventType.CREATED, event.src_path)
        self._schedule_debounced_event(created_event)

        file_add_event = self._create_event(EventType.FILE_ADD, event.src_path)
        self._schedule_debounced_event(file_add_event)

    def on_modified(self, event: FileSystemEvent):
        """Handle file modification events."""
        LOGGER.debug(f"🔍 on_modified called for: {event.src_path}, is_directory: {event.is_directory}")

        if event.is_directory:
            LOGGER.debug(f"🔍 Skipping directory: {event.src_path}")
            return

        self.stats['events_received'] += 1
        LOGGER.debug(f"🔍 File modification event received: {event.src_path}")

        if not self._should_process_file(event.src_path):
            self.stats['events_filtered'] += 1
            LOGGER.debug(f"🔍 File filtered out: {event.src_path}")
            return

        LOGGER.debug(f"🔍 Processing file modification: {event.src_path}")

        # Check if this is a newly created file (common on Windows)
        # If the file is very new (created within last few seconds), also emit FILE_ADD
        try:
            from pathlib import Path
            import time
            path_obj = Path(event.src_path)
            if path_obj.exists():
                stat_info = path_obj.stat()
                # If file was created within the last 5 seconds, treat as new file
                if time.time() - stat_info.st_ctime < 5.0:
                    LOGGER.debug(f"🔍 File is new (created within 5s), emitting FILE_ADD: {event.src_path}")
                    file_add_event = self._create_event(EventType.FILE_ADD, event.src_path)
                    self._schedule_debounced_event(file_add_event)
        except (OSError, IOError):
            pass  # Ignore errors, just proceed with normal modification event

        file_event = self._create_event(EventType.MODIFIED, event.src_path)
        self._schedule_debounced_event(file_event)

    def on_deleted(self, event: FileSystemEvent):
        """Handle file deletion events."""
        if event.is_directory:
            return

        self.stats['events_received'] += 1

        # For deleted files, we can't check filters, so we process all
        file_event = self._create_event(EventType.DELETED, event.src_path)
        self._schedule_debounced_event(file_event)

    def on_moved(self, event: FileSystemEvent):
        """Handle file move/rename events."""
        if event.is_directory:
            return

        self.stats['events_received'] += 1

        if not self._should_process_file(event.dest_path):
            self.stats['events_filtered'] += 1
            return

        # Emit both MOVED (for backward compatibility) and FILE_MOVE events
        moved_event = self._create_event(EventType.MOVED, event.src_path, event.dest_path)
        self._schedule_debounced_event(moved_event)

        file_move_event = self._create_event(EventType.FILE_MOVE, event.src_path, event.dest_path)
        self._schedule_debounced_event(file_move_event)

    def get_stats(self) -> Dict[str, int]:
        """Get event handler statistics."""
        return self.stats.copy()


class FileMonitorService:
    """Main file monitoring service that coordinates file watching and notifications."""

    def __init__(self, config: MonitorConfig, event_callback: Optional[Callable[[FileChangeEvent], None]] = None):
        self.config = config
        self.event_callback = event_callback or self._default_event_callback

        # Core components
        self.observer = Observer()
        self.event_handler = FileEventHandler(config, self._handle_file_event)
        self.watches: Dict[str, object] = {}
        self.http_notifier = HTTPNotifier(config)

        # Service state
        self.is_running = False
        self._shutdown_event = asyncio.Event()
        self._start_time: Optional[float] = None

        # Setup logging
        setup_logging(config.log_level, config.log_file)

        LOGGER.info(f"FileMonitorService initialized with {len(config.monitored_paths)} paths")

    def _default_event_callback(self, event: FileChangeEvent) -> None:
        """Default event callback that just logs events."""
        LOGGER.info(f"File {event.event_type.value}: {event.relative_path or event.file_path}")

    def _handle_file_event(self, event: FileChangeEvent) -> None:
        """Handle file events with HTTP notifications and debug logging."""
        LOGGER.debug(f"🔄 Processing file event: {event.event_type.value} for {event.file_path}")
        
        # Call user callback
        self.event_callback(event)

        # Send HTTP notification asynchronously
        if self.http_notifier.client:
            LOGGER.debug(f"🔄 Scheduling HTTP notification for {event.file_path}")
            asyncio.create_task(self.http_notifier.send_notification(event))
        else:
            LOGGER.warning(f"⚠️ HTTP client not available, skipping notification for {event.file_path}")

    async def start(self) -> None:
        """Start the file monitoring service."""
        if self.is_running:
            LOGGER.warning("Service is already running")
            return

        LOGGER.info("Starting file monitoring service...")

        try:
            # Set the event loop for the event handler
            loop = asyncio.get_running_loop()
            self.event_handler.set_event_loop(loop)

            # Validate monitored paths
            valid_paths = []
            for path in self.config.monitored_paths:
                path_obj = Path(path)
                if path_obj.exists():
                    valid_paths.append(str(path_obj.resolve()))
                    LOGGER.info(f"Monitoring path: {path}")
                else:
                    LOGGER.warning(f"Path does not exist: {path}")

            if not valid_paths:
                raise ValueError("No valid paths to monitor")

            self.config.monitored_paths = valid_paths

            # Start HTTP notifier
            await self.http_notifier.start()

            # Start file system monitoring
            for path in self.config.monitored_paths:
                watch = self.observer.schedule(
                    self.event_handler,
                    path,
                    recursive=self.config.recursive
                )
                self.watches[path] = watch

            self.observer.start()
            self.is_running = True
            self._start_time = time.time()

            LOGGER.info("✅ File monitoring service started successfully")

        except Exception as e:
            LOGGER.error(f"❌ Failed to start service: {e}")
            await self.stop()
            raise

    async def stop(self) -> None:
        """Stop the file monitoring service."""
        if not self.is_running:
            return

        LOGGER.info("Stopping file monitoring service...")

        try:
            # Stop file system monitoring
            if self.observer.is_alive():
                self.observer.stop()
                self.observer.join(timeout=5.0)

            # Stop HTTP notifier
            await self.http_notifier.stop()

            self.is_running = False
            self._shutdown_event.set()

            LOGGER.info("✅ File monitoring service stopped")

        except Exception as e:
            LOGGER.error(f"❌ Error stopping service: {e}")

    async def run_forever(self) -> None:
        """Run the service until shutdown is requested."""
        if not self.is_running:
            raise RuntimeError("Service is not running")

        LOGGER.info("🔄 Service running... Press Ctrl+C to stop")

        try:
            await self._shutdown_event.wait()
        except asyncio.CancelledError:
            LOGGER.info("Service cancelled")

    def get_status(self) -> Dict[str, Any]:
        """Get service status information."""
        uptime = time.time() - self._start_time if self._start_time else 0

        return {
            'is_running': self.is_running,
            'uptime_seconds': uptime,
            'monitored_paths': self.config.monitored_paths,
            'event_stats': self.event_handler.get_stats(),
            'notification_stats': self.http_notifier.get_stats(),
            'config': {
                'recursive': self.config.recursive,
                'debounce_interval': self.config.debounce_interval,
                'notification_urls': self.config.notification_urls,
                'web_server_port': self.config.web_server_port
            }
        }

    def add_monitored_path(self, path: str) -> None:
        """Add a new path to monitoring."""
        path_obj = Path(path).resolve()
        resolved_path = str(path_obj)

        if not path_obj.exists():
            raise ValueError(f"Path does not exist: {path}")

        if resolved_path in self.watches:
            LOGGER.warning(f"Path already monitored: {resolved_path}")
            return

        if self.is_running:
            watch = self.observer.schedule(
                self.event_handler,
                resolved_path,
                recursive=self.config.recursive
            )
            self.watches[resolved_path] = watch

        if resolved_path not in self.config.monitored_paths:
            self.config.monitored_paths.append(resolved_path)

        LOGGER.info(f"Added monitored path: {resolved_path}")

    def remove_monitored_path(self, path: str) -> None:
        """Remove a path from monitoring."""
        path_obj = Path(path).resolve()
        resolved_path = str(path_obj)

        if resolved_path in self.watches:
            watch = self.watches[resolved_path]
            self.observer.unschedule(watch)
            del self.watches[resolved_path]

        if resolved_path in self.config.monitored_paths:
            self.config.monitored_paths.remove(resolved_path)

        LOGGER.info(f"Removed monitored path: {resolved_path}")
