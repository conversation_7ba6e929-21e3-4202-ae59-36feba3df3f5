# Standalone File Monitor Service

A single-file Python application that monitors file changes and sends HTTP notifications. This service is designed to be easily converted to a binary executable and run independently.

## Features

- **Single File Application**: Everything consolidated into one Python file
- **Real-time File Monitoring**: Detects file creation, modification, deletion, and move operations
- **HTTP Notifications**: Sends POST requests to configurable endpoints when files change
- **Web Server**: Built-in web server for health checks and status monitoring
- **Intelligent Filtering**: Configurable file type, directory, and pattern filtering
- **Debouncing**: Prevents excessive processing during rapid file changes
- **Comprehensive Logging**: Detailed logging with configurable levels
- **Error Handling**: Robust error handling with retry mechanisms
- **Cross-platform**: Works on Windows, macOS, and Linux

## Requirements

- Python 3.8+
- Dependencies: `watchdog`, `httpx`, `uvicorn`, `fastapi`

## Installation

Install the required dependencies:

```bash
pip install watchdog httpx uvicorn fastapi
```

## Quick Start

### Basic Usage

```bash
# Monitor current directory
python standalone_file_monitor.py .

# Monitor specific directory
python standalone_file_monitor.py /path/to/monitor

# Monitor with custom settings
python standalone_file_monitor.py /path/to/monitor --log-level DEBUG --debounce 1.0
```

### Using Configuration File

```bash
# Create example configuration
python standalone_file_monitor.py --create-config config.json

# Run with configuration file
python standalone_file_monitor.py --config config.json
```

### Custom Notification URL

```bash
# Send notifications to custom endpoint
python standalone_file_monitor.py /path/to/monitor --notification-url http://localhost:8080/webhook
```

## HTTP Notifications

When a file change is detected, the service sends a POST request to the configured endpoint with the following JSON payload:

```json
{
  "file_path": "c:/Users/<USER>/Desktop/amul-backend/src/controllers/testEmailController.ts"
}
```

**Default notification endpoint**: `http://localhost:45213/watchdog/file_update`

## Web Server

The service includes a built-in web server (default port 45213) with the following endpoints:

- `GET /` - Root endpoint
- `GET /health` - Health check
- `GET /status` - Service status and statistics
- `GET /stats` - Detailed statistics
- `POST /shutdown` - Graceful shutdown

Example:
```bash
curl http://localhost:45213/health
curl http://localhost:45213/status
```

## Configuration

### Command Line Options

```
--config CONFIG           Path to configuration file (JSON)
--create-config FILE       Create example configuration file
--recursive                Monitor directories recursively (default: True)
--log-level LEVEL          Set logging level (DEBUG, INFO, WARNING, ERROR)
--log-file FILE            Log to file instead of console
--debounce SECONDS         Debounce interval in seconds (default: 2.0)
--include-ext EXT [EXT...] File extensions to include (e.g., .py .js .md)
--exclude-ext EXT [EXT...] File extensions to exclude
--max-size BYTES           Maximum file size to monitor (default: 10MB)
--notification-url URL     URL for HTTP notifications
--web-port PORT            Web server port (default: 45213)
--no-web-server            Disable web server
```

### Configuration File Format

```json
{
  "monitored_paths": ["/path/to/monitor"],
  "recursive": true,
  "include_extensions": [".py", ".js", ".ts", ".md", ".txt", ".json"],
  "exclude_extensions": [".tmp", ".log", ".cache"],
  "exclude_directories": ["__pycache__", ".git", "node_modules", ".venv"],
  "exclude_patterns": ["*.tmp", "*.log", "*.cache", ".*", "~*"],
  "debounce_interval": 2.0,
  "max_file_size": 10485760,
  "log_level": "INFO",
  "log_file": null,
  "notification_url": "http://localhost:45213/watchdog/file_update",
  "notification_timeout": 5.0,
  "notification_retries": 3,
  "web_server_port": 45213,
  "web_server_host": "localhost",
  "enable_web_server": true
}
```

## Creating a Binary Executable

You can convert this standalone Python file to a binary executable using PyInstaller:

```bash
# Install PyInstaller
pip install pyinstaller

# Create executable
pyinstaller --onefile --name file-monitor standalone_file_monitor.py

# The executable will be in the dist/ directory
./dist/file-monitor /path/to/monitor
```

## Production Usage

For production environments, consider:

1. **Service Management**: Use systemd, Windows Service, or similar
2. **Log Rotation**: Configure log file rotation
3. **Monitoring**: Monitor the web server endpoints for health checks
4. **Resource Limits**: Set appropriate file size and path limits
5. **Security**: Restrict network access and file permissions

### Example systemd service file:

```ini
[Unit]
Description=File Monitor Service
After=network.target

[Service]
Type=simple
User=fileMonitor
WorkingDirectory=/opt/file-monitor
ExecStart=/usr/bin/python3 /opt/file-monitor/standalone_file_monitor.py --config /etc/file-monitor/config.json
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## Troubleshooting

### Common Issues

1. **Permission denied**: Ensure read access to monitored directories
2. **High CPU usage**: Increase debounce interval or add more exclusions
3. **Missing events**: Check file filters and exclusion patterns
4. **Service won't start**: Verify configuration file syntax and paths
5. **HTTP notifications failing**: Check network connectivity and endpoint availability

### Debug Mode

Enable debug logging for troubleshooting:

```bash
python standalone_file_monitor.py /path/to/monitor --log-level DEBUG
```

## License

This project is part of CodeMate.AI and follows the same licensing terms.
