{"recursive": true, "include_extensions": [".py", ".js", ".ts", ".md", ".txt", ".json", ".yaml", ".yml"], "exclude_extensions": [".tmp", ".log", ".cache"], "exclude_directories": ["__pycache__", ".git", "node_modules", ".venv", "build", "dist", ".idea", ".vscode", "target", "bin", "obj", ".cache", ".tmp", "logs"], "exclude_patterns": ["*.tmp", "*.log", "*.cache", ".*", "~*", "#*#", ".#*"], "debounce_interval": 15.0, "max_file_size": 10485760, "log_level": "INFO", "log_file": "file_monitor.log", "notification_urls": {"file_update": "http://localhost:45213/watchdog/file_update", "file_delete": "http://localhost:45213/watchdog/file_delete"}, "notification_timeout": 5.0, "notification_retries": 3, "web_server_port": 45215, "web_server_host": "localhost", "enable_web_server": true}