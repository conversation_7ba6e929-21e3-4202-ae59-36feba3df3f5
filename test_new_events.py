#!/usr/bin/env python3
"""
Test script for new file_add and file_move event types.

This script creates test scenarios to verify that the new event types
are properly detected, processed, and would be sent to HTTP endpoints.
"""

import asyncio
import json
import os
import shutil
import tempfile
import time
from pathlib import Path

# Local imports
from utils import EventType, FileChangeEvent, MonitorConfig, normalize_path_for_http
from file_monitor import FileMonitorService
from logging_utils import setup_logging


class TestEventCollector:
    """Collects events for testing purposes."""
    
    def __init__(self):
        self.events = []
    
    def handle_event(self, event: FileChangeEvent):
        """Handle file events by collecting them."""
        self.events.append(event)
        print(f"📝 Collected event: {event.event_type.value} for {event.file_path}")
        
        # Print enhanced metadata for new event types
        if event.event_type == EventType.FILE_ADD:
            print(f"   📅 Creation time: {event.creation_time}")
            print(f"   📁 Parent directory: {event.parent_directory}")
        elif event.event_type == EventType.FILE_MOVE:
            print(f"   📤 Previous path: {event.previous_file_path}")
            print(f"   📥 New path: {event.new_file_path}")
    
    def get_events_by_type(self, event_type: EventType):
        """Get events of a specific type."""
        return [event for event in self.events if event.event_type == event_type]
    
    def clear(self):
        """Clear collected events."""
        self.events.clear()


async def test_file_add_events():
    """Test file_add event detection."""
    print("\n🧪 Testing FILE_ADD events...")
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Test directory: {temp_dir}")
        
        # Setup event collector
        collector = TestEventCollector()
        
        # Configure monitoring service
        config = MonitorConfig(
            monitored_paths=[temp_dir],
            recursive=True,
            debounce_interval=0.5,  # Shorter for testing
            log_level="DEBUG"
        )
        
        # Create monitoring service
        service = FileMonitorService(config, collector.handle_event)
        
        try:
            # Start monitoring
            await service.start()
            print("✅ Monitoring service started")
            
            # Wait a moment for service to initialize
            await asyncio.sleep(1)
            
            # Create test files
            test_files = [
                Path(temp_dir) / "test_file_1.txt",
                Path(temp_dir) / "test_file_2.py",
                Path(temp_dir) / "subdir" / "nested_file.json"
            ]
            
            # Create subdirectory
            (Path(temp_dir) / "subdir").mkdir(exist_ok=True)
            
            # Create files and wait for events
            for test_file in test_files:
                print(f"📝 Creating file: {test_file}")
                test_file.write_text(f"Test content for {test_file.name}")
                await asyncio.sleep(1)  # Wait for event processing
            
            # Wait for debouncing
            await asyncio.sleep(2)
            
            # Check collected events
            file_add_events = collector.get_events_by_type(EventType.FILE_ADD)
            print(f"\n📊 Collected {len(file_add_events)} FILE_ADD events")
            
            for event in file_add_events:
                print(f"   📄 File: {event.file_path}")
                print(f"   🔗 Normalized: {normalize_path_for_http(event.file_path)}")
                print(f"   📅 Creation time: {event.creation_time}")
                print(f"   📁 Parent dir: {event.parent_directory}")
                print(f"   📏 File size: {event.file_size}")
                print(f"   🏷️ Extension: {event.file_extension}")
                print()
            
        finally:
            await service.stop()
            print("🛑 Monitoring service stopped")


async def test_file_move_events():
    """Test file_move event detection."""
    print("\n🧪 Testing FILE_MOVE events...")
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Test directory: {temp_dir}")
        
        # Setup event collector
        collector = TestEventCollector()
        
        # Configure monitoring service
        config = MonitorConfig(
            monitored_paths=[temp_dir],
            recursive=True,
            debounce_interval=0.5,  # Shorter for testing
            log_level="DEBUG"
        )
        
        # Create monitoring service
        service = FileMonitorService(config, collector.handle_event)
        
        try:
            # Start monitoring
            await service.start()
            print("✅ Monitoring service started")
            
            # Wait a moment for service to initialize
            await asyncio.sleep(1)
            
            # Create initial test files
            source_file = Path(temp_dir) / "source_file.txt"
            source_file.write_text("Test content for move operation")
            
            # Create subdirectories
            (Path(temp_dir) / "subdir1").mkdir(exist_ok=True)
            (Path(temp_dir) / "subdir2").mkdir(exist_ok=True)
            
            await asyncio.sleep(1)  # Wait for creation events
            collector.clear()  # Clear creation events
            
            # Test file moves
            move_operations = [
                (source_file, Path(temp_dir) / "renamed_file.txt"),  # Rename in same directory
                (Path(temp_dir) / "renamed_file.txt", Path(temp_dir) / "subdir1" / "moved_file.txt"),  # Move to subdirectory
                (Path(temp_dir) / "subdir1" / "moved_file.txt", Path(temp_dir) / "subdir2" / "final_file.txt")  # Move between subdirectories
            ]
            
            for src, dest in move_operations:
                print(f"🔄 Moving: {src} -> {dest}")
                shutil.move(str(src), str(dest))
                await asyncio.sleep(1)  # Wait for event processing
            
            # Wait for debouncing
            await asyncio.sleep(2)
            
            # Check collected events
            file_move_events = collector.get_events_by_type(EventType.FILE_MOVE)
            print(f"\n📊 Collected {len(file_move_events)} FILE_MOVE events")
            
            for event in file_move_events:
                print(f"   📤 Previous: {event.previous_file_path}")
                print(f"   📥 New: {event.new_file_path}")
                print(f"   🔗 Original src: {event.file_path}")
                print(f"   🔗 Original dest: {event.dest_path}")
                print()
            
        finally:
            await service.stop()
            print("🛑 Monitoring service stopped")


async def test_payload_structure():
    """Test the payload structure for new event types."""
    print("\n🧪 Testing payload structure...")
    
    # Test FILE_ADD event payload
    file_add_event = FileChangeEvent(
        event_type=EventType.FILE_ADD,
        file_path=r"C:\Users\<USER>\Documents\new_file.txt",
        timestamp=time.time(),
        file_size=1024,
        file_extension=".txt"
    )
    
    print("📦 FILE_ADD event payload:")
    payload = file_add_event.to_dict()
    print(json.dumps(payload, indent=2))
    
    # Test FILE_MOVE event payload
    file_move_event = FileChangeEvent(
        event_type=EventType.FILE_MOVE,
        file_path=r"C:\Users\<USER>\Documents\old_file.txt",
        dest_path=r"C:\Users\<USER>\Downloads\new_file.txt",
        timestamp=time.time(),
        file_size=2048,
        file_extension=".txt"
    )
    
    print("\n📦 FILE_MOVE event payload:")
    payload = file_move_event.to_dict()
    print(json.dumps(payload, indent=2))


async def main():
    """Run all tests."""
    print("🚀 Starting tests for new event types...")
    
    # Setup logging
    setup_logging("DEBUG")
    
    try:
        # Test payload structure (no file system operations)
        await test_payload_structure()
        
        # Test file add events
        await test_file_add_events()
        
        # Test file move events
        await test_file_move_events()
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
