#!/usr/bin/env python3
"""
Test script to verify automatic path reloading functionality.
This script runs the file monitor with a shorter reload interval for testing.
"""

import asyncio
import json
import sys
from pathlib import Path

# Local imports
from logging_utils import LOGGER
from utils import FileChangeEvent, MonitorConfig, PathReloader, load_paths_automatically
from file_monitor import FileMonitorService


def event_callback(event: FileChangeEvent) -> None:
    """Test event callback."""
    print(f"📁 TEST EVENT: {event.event_type.value.upper()}: {event.relative_path or event.file_path}")


class TestFileMonitor:
    """Test application class for path reloading."""

    def __init__(self):
        # Create a minimal config
        self.config = MonitorConfig(
            monitored_paths=[],
            recursive=True,
            debounce_interval=1.0,
            log_level="INFO",
            enable_web_server=False,
            notification_urls={
                "file_update": "http://localhost:45312/watch_dog/file_update",
                "file_delete": "http://localhost:45312/watch_dog/file_delete"
            }
        )
        
        # Load initial paths
        auto_paths = load_paths_automatically()
        if auto_paths:
            self.config.monitored_paths = auto_paths
            print(f"✅ Loaded {len(auto_paths)} path(s) from .codemate/file_path.json")
        else:
            print("❌ No paths found in .codemate/file_path.json")
            sys.exit(1)
        
        self.monitor_service = FileMonitorService(self.config, event_callback)
        # Use a shorter reload interval for testing (30 seconds instead of 5 minutes)
        self.path_reloader = PathReloader(self.monitor_service, reload_interval=30.0)
        self._shutdown_requested = False

    async def start(self):
        """Start all services."""
        print("🚀 Starting Test File Monitor Service")

        # Start file monitoring
        await self.monitor_service.start()

        # Start automatic path reloading
        await self.path_reloader.start()

        # Print startup information
        self._print_startup_info()

    async def stop(self):
        """Stop all services."""
        if self._shutdown_requested:
            return

        self._shutdown_requested = True
        print("🛑 Shutting down services...")

        # Stop path reloader
        await self.path_reloader.stop()

        # Stop file monitoring
        await self.monitor_service.stop()

        print("✅ All services stopped")

    async def run_forever(self):
        """Run the application until shutdown."""
        try:
            await self.monitor_service.run_forever()
        except asyncio.CancelledError:
            print("Application cancelled")
        finally:
            await self.stop()

    def _print_startup_info(self):
        """Print startup information."""
        print(f"📂 Monitoring {len(self.config.monitored_paths)} path(s):")
        for path in self.config.monitored_paths:
            print(f"   • {path}")
        print(f"🔄 Auto-reload: Every 30 seconds from .codemate/file_path.json")
        print(f"📝 Log level: {self.config.log_level}")
        print("\nPress Ctrl+C to stop monitoring...")
        print("\n💡 To test auto-reload, modify .codemate/file_path.json and wait 30 seconds\n")


async def main():
    """Main test entry point."""
    try:
        app = TestFileMonitor()
        await app.start()
        await app.run_forever()
    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return 1
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
