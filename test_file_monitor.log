2025-07-27 16:03:06,775 - file_monitor - INFO - info:65 - Added file logging to: test_file_monitor.log
2025-07-27 16:03:06,775 - file_monitor - INFO - info:65 - Logging initialized
2025-07-27 16:03:06,775 - file_monitor - INFO - info:65 - FileMonitorService initialized with 1 paths
2025-07-27 16:03:06,776 - file_monitor - INFO - info:65 - 🚀 Starting Standalone File Monitor Service
2025-07-27 16:03:06,776 - file_monitor - INFO - info:65 - Starting file monitoring service...
2025-07-27 16:03:06,776 - file_monitor - INFO - info:65 - Monitoring path: C:\Users\<USER>\Desktop\amul-backend\src
2025-07-27 16:03:06,777 - file_monitor - DEBUG - debug:61 - 🔧 Initializing HTTP client with configuration:
2025-07-27 16:03:06,777 - file_monitor - DEBUG - debug:61 - 🔧 Timeout: 5.0s
2025-07-27 16:03:06,777 - file_monitor - DEBUG - debug:61 - 🔧 Retries: 3
2025-07-27 16:03:06,777 - file_monitor - DEBUG - debug:61 - 🔧 Endpoints: {
  "file_update": "http://localhost:45217/watchdog/file_update",
  "file_delete": "http://localhost:45217/watchdog/file_delete"
}
2025-07-27 16:03:06,873 - file_monitor - INFO - info:65 - HTTP notifier initialized with endpoints: ['http://localhost:45217/watchdog/file_update', 'http://localhost:45217/watchdog/file_delete']
2025-07-27 16:03:06,873 - file_monitor - DEBUG - debug:61 - ✅ HTTP client created successfully
2025-07-27 16:03:06,873 - file_monitor - DEBUG - debug:61 - ✅ Client timeout configuration: connect=5.0, read=5.0, write=5.0, pool=5.0
2025-07-27 16:03:06,875 - file_monitor - INFO - info:65 - ✅ File monitoring service started successfully
2025-07-27 16:03:06,876 - file_monitor - INFO - info:65 - 🔄 Service running... Press Ctrl+C to stop
2025-07-27 16:03:49,281 - file_monitor - INFO - info:65 - Added file logging to: test_file_monitor.log
2025-07-27 16:03:49,281 - file_monitor - INFO - info:65 - Logging initialized
2025-07-27 16:03:49,281 - file_monitor - INFO - info:65 - FileMonitorService initialized with 1 paths
2025-07-27 16:03:49,281 - file_monitor - INFO - info:65 - 🚀 Starting Standalone File Monitor Service
2025-07-27 16:03:49,281 - file_monitor - INFO - info:65 - Starting file monitoring service...
2025-07-27 16:03:49,282 - file_monitor - INFO - info:65 - Monitoring path: C:\Users\<USER>\Desktop\amul-backend\src
2025-07-27 16:03:49,283 - file_monitor - DEBUG - debug:61 - 🔧 Initializing HTTP client with configuration:
2025-07-27 16:03:49,283 - file_monitor - DEBUG - debug:61 - 🔧 Timeout: 5.0s
2025-07-27 16:03:49,283 - file_monitor - DEBUG - debug:61 - 🔧 Retries: 3
2025-07-27 16:03:49,283 - file_monitor - DEBUG - debug:61 - 🔧 Endpoints: {
  "file_update": "http://localhost:45217/watchdog/file_update",
  "file_delete": "http://localhost:45217/watchdog/file_delete"
}
2025-07-27 16:03:49,346 - file_monitor - INFO - info:65 - HTTP notifier initialized with endpoints: ['http://localhost:45217/watchdog/file_update', 'http://localhost:45217/watchdog/file_delete']
2025-07-27 16:03:49,346 - file_monitor - DEBUG - debug:61 - ✅ HTTP client created successfully
2025-07-27 16:03:49,346 - file_monitor - DEBUG - debug:61 - ✅ Client timeout configuration: connect=5.0, read=5.0, write=5.0, pool=5.0
2025-07-27 16:03:49,347 - file_monitor - INFO - info:65 - ✅ File monitoring service started successfully
2025-07-27 16:03:49,349 - file_monitor - INFO - info:65 - 🔄 Service running... Press Ctrl+C to stop
2025-07-27 16:08:14,787 - file_monitor - INFO - info:65 - Added file logging to: test_file_monitor.log
2025-07-27 16:08:14,787 - file_monitor - INFO - info:65 - Logging initialized
2025-07-27 16:08:14,787 - file_monitor - INFO - info:65 - FileMonitorService initialized with 1 paths
2025-07-27 16:08:14,788 - file_monitor - INFO - info:65 - 🚀 Starting Standalone File Monitor Service
2025-07-27 16:08:14,788 - file_monitor - INFO - info:65 - Starting file monitoring service...
2025-07-27 16:08:14,788 - file_monitor - INFO - info:65 - Monitoring path: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir
2025-07-27 16:08:14,789 - file_monitor - DEBUG - debug:61 - 🔧 Initializing HTTP client with configuration:
2025-07-27 16:08:14,789 - file_monitor - DEBUG - debug:61 - 🔧 Timeout: 5.0s
2025-07-27 16:08:14,789 - file_monitor - DEBUG - debug:61 - 🔧 Retries: 3
2025-07-27 16:08:14,789 - file_monitor - DEBUG - debug:61 - 🔧 Endpoints: {
  "file_update": "http://localhost:45217/watchdog/file_update",
  "file_delete": "http://localhost:45217/watchdog/file_delete"
}
2025-07-27 16:08:14,853 - file_monitor - INFO - info:65 - HTTP notifier initialized with endpoints: ['http://localhost:45217/watchdog/file_update', 'http://localhost:45217/watchdog/file_delete']
2025-07-27 16:08:14,854 - file_monitor - DEBUG - debug:61 - ✅ HTTP client created successfully
2025-07-27 16:08:14,854 - file_monitor - DEBUG - debug:61 - ✅ Client timeout configuration: connect=5.0, read=5.0, write=5.0, pool=5.0
2025-07-27 16:08:14,856 - file_monitor - INFO - info:65 - ✅ File monitoring service started successfully
2025-07-27 16:08:14,856 - file_monitor - INFO - info:65 - 🔄 Service running... Press Ctrl+C to stop
2025-07-27 16:10:06,113 - file_monitor - INFO - info:65 - Added file logging to: test_file_monitor.log
2025-07-27 16:10:06,113 - file_monitor - INFO - info:65 - Logging initialized
2025-07-27 16:10:06,113 - file_monitor - INFO - info:65 - FileMonitorService initialized with 1 paths
2025-07-27 16:10:06,114 - file_monitor - INFO - info:65 - 🚀 Starting Standalone File Monitor Service
2025-07-27 16:10:06,114 - file_monitor - INFO - info:65 - Starting file monitoring service...
2025-07-27 16:10:06,114 - file_monitor - INFO - info:65 - Monitoring path: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir
2025-07-27 16:10:06,114 - file_monitor - DEBUG - debug:61 - 🔧 Initializing HTTP client with configuration:
2025-07-27 16:10:06,114 - file_monitor - DEBUG - debug:61 - 🔧 Timeout: 5.0s
2025-07-27 16:10:06,114 - file_monitor - DEBUG - debug:61 - 🔧 Retries: 3
2025-07-27 16:10:06,114 - file_monitor - DEBUG - debug:61 - 🔧 Endpoints: {
  "file_update": "http://localhost:45217/watchdog/file_update",
  "file_delete": "http://localhost:45217/watchdog/file_delete"
}
2025-07-27 16:10:06,204 - file_monitor - INFO - info:65 - HTTP notifier initialized with endpoints: ['http://localhost:45217/watchdog/file_update', 'http://localhost:45217/watchdog/file_delete']
2025-07-27 16:10:06,205 - file_monitor - DEBUG - debug:61 - ✅ HTTP client created successfully
2025-07-27 16:10:06,205 - file_monitor - DEBUG - debug:61 - ✅ Client timeout configuration: connect=5.0, read=5.0, write=5.0, pool=5.0
2025-07-27 16:10:06,206 - file_monitor - INFO - info:65 - ✅ File monitoring service started successfully
2025-07-27 16:10:06,208 - file_monitor - INFO - info:65 - 🔄 Service running... Press Ctrl+C to stop
2025-07-27 16:10:23,075 - file_monitor - DEBUG - debug:61 - 🔍 on_created called for: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt, is_directory: False
2025-07-27 16:10:23,075 - file_monitor - DEBUG - debug:61 - 🔍 File creation event received: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:10:23,084 - file_monitor - DEBUG - debug:61 - 🔍 Processing file creation: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:10:23,085 - file_monitor - DEBUG - debug:61 - 🔍 on_modified called for: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt, is_directory: False
2025-07-27 16:10:23,085 - file_monitor - DEBUG - debug:61 - 🔍 File modification event received: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:10:23,086 - file_monitor - DEBUG - debug:61 - 🔍 Processing file modification: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:10:23,086 - file_monitor - DEBUG - debug:61 - 🔍 File is new (created within 5s), emitting FILE_ADD: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:11:50,657 - file_monitor - INFO - info:65 - Added file logging to: test_file_monitor.log
2025-07-27 16:11:50,657 - file_monitor - INFO - info:65 - Logging initialized
2025-07-27 16:11:50,657 - file_monitor - INFO - info:65 - FileMonitorService initialized with 1 paths
2025-07-27 16:11:50,657 - file_monitor - INFO - info:65 - 🚀 Starting Standalone File Monitor Service
2025-07-27 16:11:50,658 - file_monitor - INFO - info:65 - Starting file monitoring service...
2025-07-27 16:11:50,658 - file_monitor - INFO - info:65 - Monitoring path: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir
2025-07-27 16:11:50,658 - file_monitor - DEBUG - debug:61 - 🔧 Initializing HTTP client with configuration:
2025-07-27 16:11:50,658 - file_monitor - DEBUG - debug:61 - 🔧 Timeout: 5.0s
2025-07-27 16:11:50,658 - file_monitor - DEBUG - debug:61 - 🔧 Retries: 3
2025-07-27 16:11:50,658 - file_monitor - DEBUG - debug:61 - 🔧 Endpoints: {
  "file_update": "http://localhost:45217/watchdog/file_update",
  "file_delete": "http://localhost:45217/watchdog/file_delete"
}
2025-07-27 16:11:50,723 - file_monitor - INFO - info:65 - HTTP notifier initialized with endpoints: ['http://localhost:45217/watchdog/file_update', 'http://localhost:45217/watchdog/file_delete']
2025-07-27 16:11:50,724 - file_monitor - DEBUG - debug:61 - ✅ HTTP client created successfully
2025-07-27 16:11:50,724 - file_monitor - DEBUG - debug:61 - ✅ Client timeout configuration: connect=5.0, read=5.0, write=5.0, pool=5.0
2025-07-27 16:11:50,726 - file_monitor - INFO - info:65 - ✅ File monitoring service started successfully
2025-07-27 16:11:50,727 - file_monitor - INFO - info:65 - 🔄 Service running... Press Ctrl+C to stop
2025-07-27 16:12:06,726 - file_monitor - DEBUG - debug:61 - 🔍 on_created called for: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt, is_directory: False
2025-07-27 16:12:06,726 - file_monitor - DEBUG - debug:61 - 🔍 File creation event received: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt
2025-07-27 16:12:06,735 - file_monitor - DEBUG - debug:61 - 🔍 Processing file creation: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt
2025-07-27 16:12:06,735 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt, event_type: created
2025-07-27 16:12:06,735 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:12:06,736 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task
2025-07-27 16:12:06,736 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt, event_type: file_add
2025-07-27 16:12:06,736 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:12:06,736 - file_monitor - DEBUG - debug:61 - 🔍 Cancelling existing debounce task
2025-07-27 16:12:06,736 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task
2025-07-27 16:12:06,736 - file_monitor - DEBUG - debug:61 - 🔍 on_modified called for: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt, is_directory: False
2025-07-27 16:12:06,736 - file_monitor - DEBUG - debug:61 - 🔍 File modification event received: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt
2025-07-27 16:12:06,737 - file_monitor - DEBUG - debug:61 - 🔍 Processing file modification: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt
2025-07-27 16:12:06,737 - file_monitor - DEBUG - debug:61 - 🔍 File is new (created within 5s), emitting FILE_ADD: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt
2025-07-27 16:12:06,737 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt, event_type: file_add
2025-07-27 16:12:06,738 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:12:06,738 - file_monitor - DEBUG - debug:61 - 🔍 Cancelling existing debounce task
2025-07-27 16:12:06,738 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task
2025-07-27 16:12:06,738 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt, event_type: modified
2025-07-27 16:12:06,738 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:12:06,739 - file_monitor - DEBUG - debug:61 - 🔍 Cancelling existing debounce task
2025-07-27 16:12:06,739 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task
2025-07-27 16:13:12,039 - file_monitor - DEBUG - debug:61 - 🔍 on_modified called for: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt, is_directory: False
2025-07-27 16:13:12,040 - file_monitor - DEBUG - debug:61 - 🔍 File modification event received: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt
2025-07-27 16:13:12,047 - file_monitor - DEBUG - debug:61 - 🔍 Processing file modification: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt
2025-07-27 16:13:12,047 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt, event_type: modified
2025-07-27 16:13:12,047 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:13:12,047 - file_monitor - DEBUG - debug:61 - 🔍 Cancelling existing debounce task
2025-07-27 16:13:12,048 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task
2025-07-27 16:13:12,048 - file_monitor - DEBUG - debug:61 - 🔍 on_modified called for: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt, is_directory: False
2025-07-27 16:13:12,048 - file_monitor - DEBUG - debug:61 - 🔍 File modification event received: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt
2025-07-27 16:13:12,048 - file_monitor - DEBUG - debug:61 - 🔍 Processing file modification: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt
2025-07-27 16:13:12,048 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_5.txt, event_type: modified
2025-07-27 16:13:12,048 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:13:12,048 - file_monitor - DEBUG - debug:61 - 🔍 Cancelling existing debounce task
2025-07-27 16:13:12,048 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task
2025-07-27 16:15:32,816 - file_monitor - INFO - info:65 - Added file logging to: test_file_monitor.log
2025-07-27 16:15:32,817 - file_monitor - INFO - info:65 - Logging initialized
2025-07-27 16:15:32,817 - file_monitor - INFO - info:65 - FileMonitorService initialized with 1 paths
2025-07-27 16:15:32,817 - file_monitor - INFO - info:65 - 🚀 Starting Standalone File Monitor Service
2025-07-27 16:15:32,818 - file_monitor - INFO - info:65 - Starting file monitoring service...
2025-07-27 16:15:32,819 - file_monitor - INFO - info:65 - Monitoring path: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir
2025-07-27 16:15:32,820 - file_monitor - DEBUG - debug:61 - 🔧 Initializing HTTP client with configuration:
2025-07-27 16:15:32,820 - file_monitor - DEBUG - debug:61 - 🔧 Timeout: 5.0s
2025-07-27 16:15:32,820 - file_monitor - DEBUG - debug:61 - 🔧 Retries: 3
2025-07-27 16:15:32,820 - file_monitor - DEBUG - debug:61 - 🔧 Endpoints: {
  "file_update": "http://localhost:45217/watchdog/file_update",
  "file_delete": "http://localhost:45217/watchdog/file_delete"
}
2025-07-27 16:15:32,892 - file_monitor - INFO - info:65 - HTTP notifier initialized with endpoints: ['http://localhost:45217/watchdog/file_update', 'http://localhost:45217/watchdog/file_delete']
2025-07-27 16:15:32,893 - file_monitor - DEBUG - debug:61 - ✅ HTTP client created successfully
2025-07-27 16:15:32,893 - file_monitor - DEBUG - debug:61 - ✅ Client timeout configuration: connect=5.0, read=5.0, write=5.0, pool=5.0
2025-07-27 16:15:32,895 - file_monitor - INFO - info:65 - ✅ File monitoring service started successfully
2025-07-27 16:15:32,896 - file_monitor - INFO - info:65 - 🔄 Service running... Press Ctrl+C to stop
2025-07-27 16:15:54,996 - file_monitor - DEBUG - debug:61 - 🔍 on_created called for: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt, is_directory: False
2025-07-27 16:15:54,996 - file_monitor - DEBUG - debug:61 - 🔍 File creation event received: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:55,004 - file_monitor - DEBUG - debug:61 - 🔍 Processing file creation: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:55,005 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt, event_type: created
2025-07-27 16:15:55,005 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:15:55,005 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task using run_coroutine_threadsafe
2025-07-27 16:15:55,005 - file_monitor - DEBUG - debug:61 - 🔍 Starting debounce wait for 3.0s
2025-07-27 16:15:55,006 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt, event_type: file_add
2025-07-27 16:15:55,006 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:15:55,006 - file_monitor - DEBUG - debug:61 - 🔍 Cancelling existing debounce task
2025-07-27 16:15:55,006 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task using run_coroutine_threadsafe
2025-07-27 16:15:55,006 - file_monitor - DEBUG - debug:61 - 🔍 on_modified called for: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt, is_directory: False
2025-07-27 16:15:55,006 - file_monitor - DEBUG - debug:61 - 🔍 Starting debounce wait for 3.0s
2025-07-27 16:15:55,006 - file_monitor - DEBUG - debug:61 - 🔍 File modification event received: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:55,007 - file_monitor - DEBUG - debug:61 - 🔍 Processing file modification: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:55,007 - file_monitor - DEBUG - debug:61 - 🔍 File is new (created within 5s), emitting FILE_ADD: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:55,008 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt, event_type: file_add
2025-07-27 16:15:55,008 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:15:55,008 - file_monitor - DEBUG - debug:61 - 🔍 Cancelling existing debounce task
2025-07-27 16:15:55,008 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task using run_coroutine_threadsafe
2025-07-27 16:15:55,009 - file_monitor - DEBUG - debug:61 - 🔍 Starting debounce wait for 3.0s
2025-07-27 16:15:55,009 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt, event_type: modified
2025-07-27 16:15:55,010 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:15:55,010 - file_monitor - DEBUG - debug:61 - 🔍 Cancelling existing debounce task
2025-07-27 16:15:55,010 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task using run_coroutine_threadsafe
2025-07-27 16:15:55,010 - file_monitor - DEBUG - debug:61 - 🔍 Starting debounce wait for 3.0s
2025-07-27 16:15:58,019 - file_monitor - DEBUG - debug:61 - 🔍 Processing 1 debounced events
2025-07-27 16:15:58,019 - file_monitor - DEBUG - debug:61 - 🔍 Calling event_callback for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:58,019 - file_monitor - DEBUG - debug:61 - 🔄 Processing file event: modified for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:58,020 - file_monitor - INFO - info:65 - 📁 MODIFIED: test_file_6.txt (121 bytes)
2025-07-27 16:15:58,020 - file_monitor - DEBUG - debug:61 - 📁 File Event Details:
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 📁 Event Type: modified
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 📁 File Path: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 📁 Relative Path: test_file_6.txt
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 📁 Timestamp: 2025-07-27 16:15:55.009
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 📁 File Size: 121 bytes
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 📁 File Extension: .txt
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 📁 Event will trigger HTTP notification
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 🔄 Scheduling HTTP notification for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 🔍 Event processed successfully: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 🚀 Starting HTTP notification process for modified event
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 📁 File: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 🎯 Target URL: http://localhost:45217/watchdog/file_update
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 📦 Payload: {
  "file_path": "c:/Users/<USER>/Desktop/CodeMate.AI/kb_file_monitor/test_monitor_dir/test_file_6.txt",
  "event_type": "modified",
  "timestamp": 1753613155.0092082,
  "file_size": 121,
  "file_extension": ".txt",
  "relative_path": "test_file_6.txt"
}
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 📋 Headers: {
  "Content-Type": "application/json",
  "User-Agent": "FileMonitor/1.0",
  "X-Event-Type": "modified",
  "X-Timestamp": "1753613155.0092082"
}
2025-07-27 16:15:58,021 - file_monitor - DEBUG - debug:61 - 🔄 Attempt 1/4 for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:15:58,022 - file_monitor - DEBUG - debug:61 - 📤 Sending POST request to http://localhost:45217/watchdog/file_update
2025-07-27 16:15:58,022 - file_monitor - DEBUG - debug:61 - 📤 Request method: POST
2025-07-27 16:15:58,022 - file_monitor - DEBUG - debug:61 - 📤 Request headers: {
  "Content-Type": "application/json",
  "User-Agent": "FileMonitor/1.0",
  "X-Event-Type": "modified",
  "X-Timestamp": "1753613155.0092082"
}
2025-07-27 16:15:58,022 - file_monitor - DEBUG - debug:61 - 📤 Request body: {
  "file_path": "c:/Users/<USER>/Desktop/CodeMate.AI/kb_file_monitor/test_monitor_dir/test_file_6.txt",
  "event_type": "modified",
  "timestamp": 1753613155.0092082,
  "file_size": 121,
  "file_extension": ".txt",
  "relative_path": "test_file_6.txt"
}
2025-07-27 16:15:58,044 - file_monitor - DEBUG - debug:61 - 📥 Response received in 0.023s
2025-07-27 16:15:58,044 - file_monitor - DEBUG - debug:61 - 📥 Response status: 200
2025-07-27 16:15:58,044 - file_monitor - DEBUG - debug:61 - 📥 Response headers: {
  "date": "Sun, 27 Jul 2025 10:45:57 GMT",
  "server": "uvicorn",
  "content-length": "107",
  "content-type": "application/json"
}
2025-07-27 16:15:58,044 - file_monitor - DEBUG - debug:61 - 📥 Response body: {"status":"success","message":"File update notification received","timestamp":"2025-07-27T16:15:58.041072"}
2025-07-27 16:15:58,044 - file_monitor - DEBUG - debug:61 - 📥 Response JSON: {
  "status": "success",
  "message": "File update notification received",
  "timestamp": "2025-07-27T16:15:58.041072"
}
2025-07-27 16:15:58,044 - file_monitor - INFO - info:65 - ✅ Notification sent successfully to http://localhost:45217/watchdog/file_update
2025-07-27 16:15:58,044 - file_monitor - DEBUG - debug:61 - ✅ File: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt (modified)
2025-07-27 16:15:58,044 - file_monitor - DEBUG - debug:61 - ✅ Total duration: 0.023s
2025-07-27 16:15:58,044 - file_monitor - DEBUG - debug:61 - ✅ Attempt: 1/4
2025-07-27 16:16:22,566 - file_monitor - DEBUG - debug:61 - 🔍 on_modified called for: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt, is_directory: False
2025-07-27 16:16:22,566 - file_monitor - DEBUG - debug:61 - 🔍 File modification event received: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:22,569 - file_monitor - DEBUG - debug:61 - 🔍 Processing file modification: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:22,569 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt, event_type: modified
2025-07-27 16:16:22,569 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:16:22,569 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task using run_coroutine_threadsafe
2025-07-27 16:16:22,569 - file_monitor - DEBUG - debug:61 - 🔍 on_modified called for: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt, is_directory: False
2025-07-27 16:16:22,570 - file_monitor - DEBUG - debug:61 - 🔍 Starting debounce wait for 3.0s
2025-07-27 16:16:22,570 - file_monitor - DEBUG - debug:61 - 🔍 File modification event received: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:22,570 - file_monitor - DEBUG - debug:61 - 🔍 Processing file modification: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:22,570 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt, event_type: modified
2025-07-27 16:16:22,570 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:16:22,571 - file_monitor - DEBUG - debug:61 - 🔍 Cancelling existing debounce task
2025-07-27 16:16:22,571 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task using run_coroutine_threadsafe
2025-07-27 16:16:22,571 - file_monitor - DEBUG - debug:61 - 🔍 Starting debounce wait for 3.0s
2025-07-27 16:16:25,597 - file_monitor - DEBUG - debug:61 - 🔍 Processing 1 debounced events
2025-07-27 16:16:25,597 - file_monitor - DEBUG - debug:61 - 🔍 Calling event_callback for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:25,597 - file_monitor - DEBUG - debug:61 - 🔄 Processing file event: modified for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:25,597 - file_monitor - INFO - info:65 - 📁 MODIFIED: test_file_6.txt (170 bytes)
2025-07-27 16:16:25,600 - file_monitor - DEBUG - debug:61 - 📁 File Event Details:
2025-07-27 16:16:25,600 - file_monitor - DEBUG - debug:61 - 📁 Event Type: modified
2025-07-27 16:16:25,600 - file_monitor - DEBUG - debug:61 - 📁 File Path: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:25,600 - file_monitor - DEBUG - debug:61 - 📁 Relative Path: test_file_6.txt
2025-07-27 16:16:25,601 - file_monitor - DEBUG - debug:61 - 📁 Timestamp: 2025-07-27 16:16:22.570
2025-07-27 16:16:25,601 - file_monitor - DEBUG - debug:61 - 📁 File Size: 170 bytes
2025-07-27 16:16:25,601 - file_monitor - DEBUG - debug:61 - 📁 File Extension: .txt
2025-07-27 16:16:25,601 - file_monitor - DEBUG - debug:61 - 📁 Event will trigger HTTP notification
2025-07-27 16:16:25,601 - file_monitor - DEBUG - debug:61 - 🔄 Scheduling HTTP notification for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:25,601 - file_monitor - DEBUG - debug:61 - 🔍 Event processed successfully: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:25,602 - file_monitor - DEBUG - debug:61 - 🚀 Starting HTTP notification process for modified event
2025-07-27 16:16:25,602 - file_monitor - DEBUG - debug:61 - 📁 File: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:25,602 - file_monitor - DEBUG - debug:61 - 🎯 Target URL: http://localhost:45217/watchdog/file_update
2025-07-27 16:16:25,602 - file_monitor - DEBUG - debug:61 - 📦 Payload: {
  "file_path": "c:/Users/<USER>/Desktop/CodeMate.AI/kb_file_monitor/test_monitor_dir/test_file_6.txt",
  "event_type": "modified",
  "timestamp": 1753613182.5706825,
  "file_size": 170,
  "file_extension": ".txt",
  "relative_path": "test_file_6.txt"
}
2025-07-27 16:16:25,602 - file_monitor - DEBUG - debug:61 - 📋 Headers: {
  "Content-Type": "application/json",
  "User-Agent": "FileMonitor/1.0",
  "X-Event-Type": "modified",
  "X-Timestamp": "1753613182.5706825"
}
2025-07-27 16:16:25,602 - file_monitor - DEBUG - debug:61 - 🔄 Attempt 1/4 for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt
2025-07-27 16:16:25,602 - file_monitor - DEBUG - debug:61 - 📤 Sending POST request to http://localhost:45217/watchdog/file_update
2025-07-27 16:16:25,602 - file_monitor - DEBUG - debug:61 - 📤 Request method: POST
2025-07-27 16:16:25,602 - file_monitor - DEBUG - debug:61 - 📤 Request headers: {
  "Content-Type": "application/json",
  "User-Agent": "FileMonitor/1.0",
  "X-Event-Type": "modified",
  "X-Timestamp": "1753613182.5706825"
}
2025-07-27 16:16:25,602 - file_monitor - DEBUG - debug:61 - 📤 Request body: {
  "file_path": "c:/Users/<USER>/Desktop/CodeMate.AI/kb_file_monitor/test_monitor_dir/test_file_6.txt",
  "event_type": "modified",
  "timestamp": 1753613182.5706825,
  "file_size": 170,
  "file_extension": ".txt",
  "relative_path": "test_file_6.txt"
}
2025-07-27 16:16:25,621 - file_monitor - DEBUG - debug:61 - 📥 Response received in 0.019s
2025-07-27 16:16:25,621 - file_monitor - DEBUG - debug:61 - 📥 Response status: 200
2025-07-27 16:16:25,621 - file_monitor - DEBUG - debug:61 - 📥 Response headers: {
  "date": "Sun, 27 Jul 2025 10:46:25 GMT",
  "server": "uvicorn",
  "content-length": "107",
  "content-type": "application/json"
}
2025-07-27 16:16:25,622 - file_monitor - DEBUG - debug:61 - 📥 Response body: {"status":"success","message":"File update notification received","timestamp":"2025-07-27T16:16:25.618649"}
2025-07-27 16:16:25,622 - file_monitor - DEBUG - debug:61 - 📥 Response JSON: {
  "status": "success",
  "message": "File update notification received",
  "timestamp": "2025-07-27T16:16:25.618649"
}
2025-07-27 16:16:25,622 - file_monitor - INFO - info:65 - ✅ Notification sent successfully to http://localhost:45217/watchdog/file_update
2025-07-27 16:16:25,623 - file_monitor - DEBUG - debug:61 - ✅ File: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_6.txt (modified)
2025-07-27 16:16:25,623 - file_monitor - DEBUG - debug:61 - ✅ Total duration: 0.020s
2025-07-27 16:16:25,624 - file_monitor - DEBUG - debug:61 - ✅ Attempt: 1/4
2025-07-27 16:16:48,623 - file_monitor - DEBUG - debug:61 - 🔍 Scheduling debounced event for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt, event_type: deleted
2025-07-27 16:16:48,623 - file_monitor - DEBUG - debug:61 - 🔍 Stored event in pending_events, total pending: 1
2025-07-27 16:16:48,623 - file_monitor - DEBUG - debug:61 - 🔍 Creating new debounce task using run_coroutine_threadsafe
2025-07-27 16:16:48,623 - file_monitor - DEBUG - debug:61 - 🔍 Starting debounce wait for 3.0s
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 🔍 Processing 1 debounced events
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 🔍 Calling event_callback for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 🔄 Processing file event: deleted for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:16:51,635 - file_monitor - INFO - info:65 - 📁 DELETED: test_file_4.txt
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 📁 File Event Details:
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 📁 Event Type: deleted
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 📁 File Path: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 📁 Relative Path: test_file_4.txt
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 📁 Timestamp: 2025-07-27 16:16:48.622
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 📁 File Size: Unknown
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 📁 File Extension: .txt
2025-07-27 16:16:51,635 - file_monitor - DEBUG - debug:61 - 📁 Event will trigger HTTP notification
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 🔄 Scheduling HTTP notification for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 🔍 Event processed successfully: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 🚀 Starting HTTP notification process for deleted event
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 📁 File: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 🎯 Target URL: http://localhost:45217/watchdog/file_delete
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 📦 Payload: {
  "file_path": "c:/Users/<USER>/Desktop/CodeMate.AI/kb_file_monitor/test_monitor_dir/test_file_4.txt",
  "event_type": "deleted",
  "timestamp": 1753613208.6228473,
  "file_extension": ".txt",
  "relative_path": "test_file_4.txt"
}
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 📋 Headers: {
  "Content-Type": "application/json",
  "User-Agent": "FileMonitor/1.0",
  "X-Event-Type": "deleted",
  "X-Timestamp": "1753613208.6228473"
}
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 🔄 Attempt 1/4 for C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 📤 Sending POST request to http://localhost:45217/watchdog/file_delete
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 📤 Request method: POST
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 📤 Request headers: {
  "Content-Type": "application/json",
  "User-Agent": "FileMonitor/1.0",
  "X-Event-Type": "deleted",
  "X-Timestamp": "1753613208.6228473"
}
2025-07-27 16:16:51,636 - file_monitor - DEBUG - debug:61 - 📤 Request body: {
  "file_path": "c:/Users/<USER>/Desktop/CodeMate.AI/kb_file_monitor/test_monitor_dir/test_file_4.txt",
  "event_type": "deleted",
  "timestamp": 1753613208.6228473,
  "file_extension": ".txt",
  "relative_path": "test_file_4.txt"
}
2025-07-27 16:16:51,644 - file_monitor - DEBUG - debug:61 - 📥 Response received in 0.008s
2025-07-27 16:16:51,644 - file_monitor - DEBUG - debug:61 - 📥 Response status: 200
2025-07-27 16:16:51,644 - file_monitor - DEBUG - debug:61 - 📥 Response headers: {
  "date": "Sun, 27 Jul 2025 10:46:51 GMT",
  "server": "uvicorn",
  "content-length": "107",
  "content-type": "application/json"
}
2025-07-27 16:16:51,644 - file_monitor - DEBUG - debug:61 - 📥 Response body: {"status":"success","message":"File delete notification received","timestamp":"2025-07-27T16:16:51.643019"}
2025-07-27 16:16:51,644 - file_monitor - DEBUG - debug:61 - 📥 Response JSON: {
  "status": "success",
  "message": "File delete notification received",
  "timestamp": "2025-07-27T16:16:51.643019"
}
2025-07-27 16:16:51,645 - file_monitor - INFO - info:65 - ✅ Notification sent successfully to http://localhost:45217/watchdog/file_delete
2025-07-27 16:16:51,645 - file_monitor - DEBUG - debug:61 - ✅ File: C:\Users\<USER>\Desktop\CodeMate.AI\kb_file_monitor\test_monitor_dir\test_file_4.txt (deleted)
2025-07-27 16:16:51,645 - file_monitor - DEBUG - debug:61 - ✅ Total duration: 0.009s
2025-07-27 16:16:51,645 - file_monitor - DEBUG - debug:61 - ✅ Attempt: 1/4
