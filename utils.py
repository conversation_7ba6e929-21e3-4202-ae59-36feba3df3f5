#!/usr/bin/env python3
"""
Utility classes and functions for the File Monitor Service.

This module contains data structures, configuration classes, and utility functions
used throughout the file monitoring system.
"""

import argparse
import json
import os
import time
import asyncio
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any


class HomePathFinder:
    def __init__(self):
        pass

    def get_home_path(self) -> Path:
        """Get the user's home directory path with multiple fallback methods"""
        # Try multiple methods to get the home directory
        methods = [
            # Method 1: pathlib's Path.home() (standard way)
            lambda: Path.home(),
            # Method 2: expanduser
            lambda: Path(os.path.expanduser("~")),
            # Method 3: environment variables
            lambda: Path(os.environ.get("USERPROFILE") or os.environ.get("HOME", "")),
            # Method 4: Windows specific - try to get from registry if on Windows
            lambda: self._get_windows_home() if sys.platform == "win32" else Path(),
            # Method 5: Last resort - use current working directory
            lambda: Path(os.getcwd()),
        ]

        for method in methods:
            try:
                path = method()
                if path and path.exists() and path.is_dir():
                    return path
            except Exception:
                continue

        # If all methods fail, return current directory as last resort
        return Path(os.getcwd())

    def _get_windows_home(self) -> Path:
        """Get Windows home directory from registry as fallback"""
        try:
            import winreg

            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders",
            ) as key:
                home = winreg.QueryValueEx(key, "Personal")[0]
                return Path(
                    home
                ).parent  # Get parent of Documents folder which should be user home
        except Exception:
            return Path()





class PathSelector:
    """Utility class for locating configuration paths."""


    @staticmethod
    def get_user_home_path() -> Path:
        """Get the user's home directory path"""
        return HomePathFinder().get_home_path()
    


    @staticmethod
    def get_base_path() -> Path:
        """Get the .codemate directory path.

        This method looks for the .codemate directory starting from the current
        working directory and traversing up the directory tree until it finds it
        or reaches the root directory.

        Returns:
            Path to the .codemate directory

        Raises:
            FileNotFoundError: If .codemate directory is not found
        """
        home_path = PathSelector.get_user_home_path()

        # First check if .codemate exists in current directory
        codemate_path = home_path / ".codemate"
        if codemate_path.exists() and codemate_path.is_dir():
            return codemate_path
        
        return codemate_path


class EventType(Enum):
    """File system event types."""
    CREATED = "created"
    MODIFIED = "modified"
    DELETED = "deleted"
    MOVED = "moved"
    FILE_ADD = "file_add"
    FILE_MOVE = "file_move"


@dataclass
class FileChangeEvent:
    """Represents a file system change event."""
    event_type: EventType
    file_path: str
    dest_path: Optional[str] = None  # For move events
    timestamp: float = 0.0
    file_size: Optional[int] = None
    file_extension: Optional[str] = None
    relative_path: Optional[str] = None

    # Enhanced metadata for new event types
    creation_time: Optional[float] = None  # For file_add events
    parent_directory: Optional[str] = None  # For file_add events
    previous_file_path: Optional[str] = None  # For file_move events (normalized)
    new_file_path: Optional[str] = None  # For file_move events (normalized)
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()

        # Extract file metadata
        if self.file_path:
            path_obj = Path(self.file_path)
            self.file_extension = path_obj.suffix.lower() if path_obj.suffix else None

            # Get file size for existing files
            if path_obj.exists() and not path_obj.is_dir():
                try:
                    stat_info = path_obj.stat()
                    self.file_size = stat_info.st_size

                    # Set creation time for file_add events
                    if self.event_type == EventType.FILE_ADD:
                        self.creation_time = stat_info.st_ctime
                        self.parent_directory = normalize_path_for_http(str(path_obj.parent))
                except (OSError, IOError):
                    self.file_size = None

            # Handle file_move events with normalized paths
            if self.event_type == EventType.FILE_MOVE:
                self.previous_file_path = normalize_path_for_http(self.file_path)
                self.new_file_path = normalize_path_for_http(self.dest_path) if self.dest_path else None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with JSON-serializable values."""
        data = asdict(self)
        # Convert EventType enum to string value
        data['event_type'] = self.event_type.value
        return data


@dataclass
class MonitorConfig:
    """Configuration for file monitoring."""
    # Paths to monitor
    monitored_paths: List[str] = None
    recursive: bool = True
    
    # File filtering
    include_extensions: List[str] = None
    exclude_extensions: List[str] = None
    exclude_directories: List[str] = None
    exclude_patterns: List[str] = None
    
    # Performance
    debounce_interval: float = 2.0
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    # HTTP Notifications
    notification_urls: Dict[str, str] = None
    notification_timeout: float = 5.0
    notification_retries: int = 3
    
    # Web Server
    web_server_port: int = 45213
    web_server_host: str = "localhost"
    enable_web_server: bool = True
    
    def __post_init__(self):
        """Initialize default values."""
        if self.monitored_paths is None:
            self.monitored_paths = []
        if self.include_extensions is None:
            self.include_extensions = []
        if self.exclude_extensions is None:
            self.exclude_extensions = [".tmp", ".log", ".cache"]
        if self.exclude_directories is None:
            self.exclude_directories = [
                "__pycache__", ".git", "node_modules", ".venv", "build", "dist",
                ".idea", ".vscode", "target", "bin", "obj", ".cache", ".tmp", "logs"
            ]
        if self.exclude_patterns is None:
            self.exclude_patterns = ["*.tmp", "*.log", "*.cache", ".*", "~*", "#*#", ".#*"]
        if self.notification_urls is None:
            self.notification_urls = {
                "file_update": "http://localhost:45213/watchdog/file_update",
                "file_delete": "http://localhost:45213/watchdog/file_update",
                "file_add": "http://localhost:45213/watchdog/file_update",
                "file_move": "http://localhost:45213/watchdog/file_update"
            }
    
    @classmethod
    def from_file(cls, config_path: str) -> 'MonitorConfig':
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return cls(**data)
        except Exception as e:
            raise ValueError(f"Failed to load config from {config_path}: {e}")
    
    def to_file(self, config_path: str) -> None:
        """Save configuration to JSON file."""
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(self), f, indent=2)
        except Exception as e:
            raise ValueError(f"Failed to save config to {config_path}: {e}")


def load_paths_from_file(paths_file: str) -> List[str]:
    """Load monitored paths from an external JSON file.

    Args:
        paths_file: Path to the JSON file containing monitored paths

    Returns:
        List of paths to monitor

    Raises:
        FileNotFoundError: If the paths file doesn't exist
        ValueError: If the JSON is malformed or missing required keys
    """
    if not Path(paths_file).exists():
        raise FileNotFoundError(f"Paths file not found: {paths_file}")

    try:
        with open(paths_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in paths file {paths_file}: {e}")
    except Exception as e:
        raise ValueError(f"Failed to read paths file {paths_file}: {e}")

    if not isinstance(data, dict):
        raise ValueError(f"Paths file {paths_file} must contain a JSON object")

    if "monitored_paths" not in data:
        raise ValueError(f"Paths file {paths_file} must contain a 'monitored_paths' key")

    monitored_paths = data["monitored_paths"]
    if not isinstance(monitored_paths, list):
        raise ValueError(f"'monitored_paths' in {paths_file} must be an array")

    if not monitored_paths:
        raise ValueError(f"'monitored_paths' array in {paths_file} cannot be empty")

    # Validate that all paths are strings
    for i, path in enumerate(monitored_paths):
        if not isinstance(path, str):
            raise ValueError(f"Path at index {i} in {paths_file} must be a string, got {type(path).__name__}")

    return monitored_paths


def load_paths_automatically() -> Optional[List[str]]:
    """Automatically locate and load monitored paths from .codemate/file_path.json.

    Returns:
        List of paths to monitor, or None if file doesn't exist or has errors
    """
    try:
        base_path = PathSelector.get_base_path()
        paths_file = base_path / "file_path.json"

        if not paths_file.exists():
            print(f"📁 Paths file not found: {paths_file}")
            return None

        print(f"📁 Loading paths from: {paths_file}")
        return load_paths_from_file(str(paths_file))

    except Exception as e:
        print(f"⚠️  Warning: Failed to load paths automatically: {e}")
        return None


def validate_and_filter_paths(paths: List[str], paths_source: str = "configuration") -> List[str]:
    """Validate and filter paths, removing non-existent ones.

    Args:
        paths: List of paths to validate
        paths_source: Description of where the paths came from (for logging)

    Returns:
        List of valid, resolved paths

    Raises:
        ValueError: If no valid paths remain after filtering
    """
    valid_paths = []

    for path in paths:
        path_obj = Path(path)
        if path_obj.exists():
            resolved_path = str(path_obj.resolve())
            valid_paths.append(resolved_path)
            print(f"✅ Valid path from {paths_source}: {resolved_path}")
        else:
            print(f"⚠️  Path does not exist (skipping): {path}")

    if not valid_paths:
        raise ValueError(f"No valid paths found in {paths_source}")

    return valid_paths


def normalize_path_for_http(file_path: str) -> str:
    """Normalize Windows file paths to Unix-style format for HTTP notifications.

    This function converts Windows-style paths to Unix-style paths by:
    1. Converting all backslashes (\\) to forward slashes (/)
    2. Converting drive letters to lowercase (e.g., C:\\ becomes c:/)
    3. Handling UNC paths and relative paths appropriately

    Args:
        file_path: The file path to normalize (can be Windows or Unix style)

    Returns:
        Normalized path in Unix-style format

    Examples:
        >>> normalize_path_for_http("C:\\\\Users\\\\<USER>\\\\Desktop\\\\file.txt")
        'c:/Users/<USER>/Desktop/file.txt'
        >>> normalize_path_for_http("D:\\\\Projects\\\\amul-backend\\\\src\\\\controllers\\\\productController.ts")
        'd:/Projects/amul-backend/src/controllers/productController.ts'
        >>> normalize_path_for_http("already/unix/style/path.txt")
        'already/unix/style/path.txt'
        >>> normalize_path_for_http("\\\\\\\\server\\\\share\\\\file.txt")
        '//server/share/file.txt'
    """
    if not file_path:
        return file_path

    # Convert to string if it's a Path object
    if isinstance(file_path, Path):
        file_path = str(file_path)

    # Convert all backslashes to forward slashes
    normalized = file_path.replace('\\', '/')

    # Handle Windows drive letters (e.g., C:/ -> c:/)
    if len(normalized) >= 2 and normalized[1] == ':':
        # Convert drive letter to lowercase
        normalized = normalized[0].lower() + normalized[1:]

    # Handle UNC paths (\\server\share -> //server/share)
    # This is already handled by the backslash replacement above

    return normalized


class PathReloader:
    """Handles automatic reloading of monitored paths from configuration file."""

    def __init__(self, monitor_service=None, reload_interval: float = 300.0):
        """Initialize the path reloader.

        Args:
            monitor_service: The file monitor service to update with new paths
            reload_interval: Interval in seconds between reload checks (default: 5 minutes)
        """
        self.monitor_service = monitor_service
        self.reload_interval = reload_interval
        self.last_reload_time = 0.0
        self.current_paths = []
        self._reload_task = None
        self._running = False

    async def start(self):
        """Start the automatic path reloading."""
        if self._running:
            return

        self._running = True
        self._reload_task = asyncio.create_task(self._reload_loop())
        print(f"🔄 Started automatic path reloading (every {self.reload_interval/60:.1f} minutes)")

    async def stop(self):
        """Stop the automatic path reloading."""
        self._running = False
        if self._reload_task:
            self._reload_task.cancel()
            try:
                await self._reload_task
            except asyncio.CancelledError:
                pass
        print("🔄 Stopped automatic path reloading")

    async def _reload_loop(self):
        """Main reload loop that runs periodically."""
        while self._running:
            try:
                await asyncio.sleep(self.reload_interval)
                if self._running:
                    await self._check_and_reload_paths()
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"⚠️  Error in path reload loop: {e}")

    async def _check_and_reload_paths(self):
        """Check for path changes and reload if necessary."""
        try:
            new_paths = load_paths_automatically()
            if new_paths is None:
                return

            # Validate and filter new paths
            try:
                valid_new_paths = validate_and_filter_paths(new_paths, "automatic reload")
            except ValueError as e:
                print(f"⚠️  Warning during path reload: {e}")
                return

            # Check if paths have changed
            if set(valid_new_paths) != set(self.current_paths):
                print(f"🔄 Detected path changes, updating monitored paths...")
                await self._update_monitored_paths(valid_new_paths)
                self.current_paths = valid_new_paths
                print(f"✅ Successfully updated to {len(valid_new_paths)} monitored path(s)")

        except Exception as e:
            print(f"⚠️  Warning: Failed to reload paths: {e}")

    async def _update_monitored_paths(self, new_paths: List[str]):
        """Update the monitor service with new paths."""
        if not self.monitor_service:
            return

        # This would need to be implemented in the monitor service
        # For now, we'll just update the config
        if hasattr(self.monitor_service, 'config'):
            self.monitor_service.config.monitored_paths = new_paths
            print(f"📁 Updated configuration with {len(new_paths)} path(s)")


def create_example_config(output_file: str) -> None:
    """Create an example configuration file."""
    config_data = {
        "recursive": True,
        "include_extensions": [".py", ".js", ".md", ".txt", ".json", ".yaml", ".yml"],
        "exclude_extensions": [".tmp", ".log", ".cache"],
        "exclude_directories": [
            "__pycache__", ".git", "node_modules", ".vscode",
            ".idea", "build", "dist", ".cache", ".venv"
        ],
        "exclude_patterns": ["*.tmp", "*.log", "*.cache", ".*", "~*"],
        "debounce_interval": 2.0,
        "max_file_size": 10485760,
        "log_level": "INFO",
        "log_file": None,
        "notification_urls": {
            "file_update": "http://localhost:45312/watch_dog/file_update",
            "file_delete": "http://localhost:45312/watch_dog/file_delete"
        },
        "notification_timeout": 5.0,
        "notification_retries": 3,
        "web_server_port": 45213,
        "web_server_host": "localhost",
        "enable_web_server": True
    }

    # Also create an example paths file
    paths_file = output_file.replace('.json', '_paths.json')
    paths_data = {
        "monitored_paths": [
            str(Path.cwd()),
            str(Path.home() / "Documents")
        ]
    }

    try:
        with open(output_file, 'w') as f:
            json.dump(config_data, f, indent=2)
        print(f"✅ Example configuration created: {output_file}")

        with open(paths_file, 'w') as f:
            json.dump(paths_data, f, indent=2)
        print(f"✅ Example paths file created: {paths_file}")
        print(f"💡 Usage: python standalone_file_monitor.py --config {output_file} --paths-file {paths_file}")
    except Exception as e:
        print(f"❌ Failed to create config file: {e}")
        import sys
        sys.exit(1)


def load_config(args: argparse.Namespace) -> MonitorConfig:
    """Load configuration from arguments or file with automatic path discovery."""
    # Load base configuration
    if args.config:
        # Load from file
        config = MonitorConfig.from_file(args.config)
    else:
        # Create from arguments
        config = MonitorConfig(
            recursive=args.recursive,
            debounce_interval=args.debounce,
            max_file_size=args.max_size,
            log_level=args.log_level,
            log_file=args.log_file,
            notification_urls={
                "file_update": args.notification_url_update,
                "file_delete": args.notification_url_delete
            },
            web_server_port=args.web_port,
            enable_web_server=not args.no_web_server
        )

        if args.include_ext:
            config.include_extensions = args.include_ext

        if args.exclude_ext:
            config.exclude_extensions = args.exclude_ext

    # Handle monitored paths - priority order:
    # 1. Command line paths (args.paths) - highest priority for explicit user control
    # 2. Config file paths (config.monitored_paths)
    # 3. Automatic discovery from .codemate/file_path.json - fallback only

    monitored_paths = []
    paths_source = ""

    if args.paths:
        # Use command line paths (highest priority)
        monitored_paths = args.paths
        paths_source = "command line arguments"
    elif config.monitored_paths:
        # Use config file paths
        monitored_paths = config.monitored_paths
        paths_source = "configuration file"
    else:
        # Fallback to automatic path discovery
        auto_paths = load_paths_automatically()
        if auto_paths:
            monitored_paths = auto_paths
            paths_source = "automatic discovery (.codemate/file_path.json)"
        else:
            print("❌ No paths specified to monitor.")
            print("💡 Create a .codemate/file_path.json file with monitored_paths, provide paths as arguments, or include monitored_paths in config file.")
            import sys
        sys.exit(1)

    # Validate and filter paths
    try:
        config.monitored_paths = validate_and_filter_paths(monitored_paths, paths_source)
        print(f"✅ Successfully loaded {len(config.monitored_paths)} valid path(s) from {paths_source}")
    except ValueError as e:
        print(f"❌ {e}")
        import sys
        sys.exit(1)

    return config


# Embedded default configuration
DEFAULT_CONFIG = {
    "recursive": True,
    "include_extensions": [".py", ".js", ".ts", ".md", ".txt", ".json", ".yaml", ".yml"],
    "exclude_extensions": [".tmp", ".log", ".cache"],
    "exclude_directories": [
        "__pycache__", ".git", "node_modules", ".venv", "build", "dist",
        ".idea", ".vscode", "target", "bin", "obj", ".cache", ".tmp", "logs"
    ],
    "exclude_patterns": ["*.tmp", "*.log", "*.cache", ".*", "~*", "#*#", ".#*"],
    "debounce_interval": 2.0,
    "max_file_size": 10485760,
    "log_level": "INFO",
    "log_file": None,
    "notification_urls": {
        "file_update": "http://localhost:45312/watch_dog/file_update",
        "file_delete": "http://localhost:45312/watch_dog/file_delete"
    },
    "notification_timeout": 5.0,
    "notification_retries": 3,
    "web_server_port": 45213,
    "web_server_host": "localhost",
    "enable_web_server": True
}

