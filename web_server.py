#!/usr/bin/env python3
"""
Web Server for File Monitor Service.

This module provides a FastAPI-based web server for health checks, status monitoring,
and service management endpoints.
"""

import asyncio
import time
from typing import Optional

# External dependencies
try:
    import uvicorn
    from fastapi import FastAPI
except ImportError as e:
    print(f"❌ Missing required dependency: {e}")
    print("Install with: pip install uvicorn fastapi")
    import sys
    sys.exit(1)

# Local imports
from logging_utils import LOGGER
from utils import MonitorConfig


class WebServer:
    """Simple web server for health checks and status monitoring."""

    def __init__(self, config: MonitorConfig, monitor_service):
        self.config = config
        self.monitor_service = monitor_service
        self.app = FastAPI(title="File Monitor Service", version="1.0.0")
        self.server_task: Optional[asyncio.Task] = None

        # Setup routes
        self._setup_routes()

    def _setup_routes(self):
        """Setup FastAPI routes."""

        @self.app.get("/")
        async def root():
            """Root endpoint."""
            return {"message": "File Monitor Service", "status": "running"}

        @self.app.get("/health")
        async def health():
            """Health check endpoint."""
            return {"status": "healthy", "timestamp": time.time()}

        @self.app.get("/status")
        async def status():
            """Get service status."""
            return self.monitor_service.get_status()

        @self.app.get("/stats")
        async def stats():
            """Get detailed statistics."""
            status = self.monitor_service.get_status()
            return {
                "service": status,
                "timestamp": time.time()
            }

        @self.app.post("/shutdown")
        async def shutdown():
            """Shutdown the service."""
            LOGGER.info("Shutdown requested via API")
            asyncio.create_task(self._delayed_shutdown())
            return {"message": "Shutdown initiated"}

    async def _delayed_shutdown(self):
        """Delayed shutdown to allow response to be sent."""
        await asyncio.sleep(1.0)
        if self.monitor_service.is_running:
            await self.monitor_service.stop()

    async def start(self):
        """Start the web server."""
        if self.server_task:
            LOGGER.warning("Web server already running")
            return

        config = uvicorn.Config(
            self.app,
            host=self.config.web_server_host,
            port=self.config.web_server_port,
            log_level="warning",  # Reduce uvicorn logging
            access_log=False
        )

        server = uvicorn.Server(config)
        self.server_task = asyncio.create_task(server.serve())

        LOGGER.info(f"🌐 Web server started on http://{self.config.web_server_host}:{self.config.web_server_port}")

    async def stop(self):
        """Stop the web server."""
        if self.server_task:
            self.server_task.cancel()
            try:
                await self.server_task
            except asyncio.CancelledError:
                pass
            self.server_task = None
            LOGGER.info("🌐 Web server stopped")
