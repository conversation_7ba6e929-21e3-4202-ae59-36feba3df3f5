#!/usr/bin/env python3
"""
Standalone File Monitor Service with HTTP Notifications

A modular application that monitors file changes and sends HTTP notifications.
Includes web server capability and comprehensive error handling.

This is the main application module that coordinates the file monitoring service.
Supporting modules:
- logging_utils.py: Logging functionality
- utils.py: Configuration classes and utility functions
- http_notifier.py: HTTP notification system
- file_monitor.py: Core file monitoring functionality
- web_server.py: Web server for status and health checks

Requirements:
- Python 3.8+
- watchdog
- uvicorn
- httpx (for async HTTP requests)
- fastapi

Usage:
    python standalone_file_monitor.py [paths...] [options]
    python standalone_file_monitor.py --config config.json
    python standalone_file_monitor.py --create-config config.json
"""

import asyncio
import argparse
import signal
import sys
import time
from pathlib import Path
from datetime import datetime

# Local imports
from logging_utils import LOGGER
from utils import FileChangeEvent, MonitorConfig, create_example_config, load_config, PathReloader
from file_monitor import FileMonitorService
from web_server import WebServer

# Note: External dependencies are now handled in individual modules


# ============================================================================
# CONFIGURATION AND DATA STRUCTURES
# ============================================================================
# Note: EventType, FileChangeEvent, and MonitorConfig are now imported from utils.py





# ============================================================================
# LOGGING SYSTEM
# ============================================================================
# Note: FileMonitorLogger, LOGGER, and setup_logging are now imported from logging_utils.py


# ============================================================================
# HTTP NOTIFICATION SYSTEM
# ============================================================================
# Note: HTTPNotifier class is now in http_notifier.py





# ============================================================================
# FILE MONITORING SYSTEM
# ============================================================================
# Note: FileEventHandler and FileMonitorService classes are now in file_monitor.py






# ============================================================================
# WEB SERVER
# ============================================================================
# Note: WebServer class is now in web_server.py



# ============================================================================
# COMMAND LINE INTERFACE
# ============================================================================

def create_parser() -> argparse.ArgumentParser:
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Standalone File Monitor - Real-time file system change detection with HTTP notifications",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s /path/to/monitor
  %(prog)s /path/to/monitor --recursive --log-level DEBUG
  %(prog)s /path/to/monitor --config config.json
  %(prog)s --config config.json
  %(prog)s --create-config config.json
  %(prog)s --notification-url-update http://localhost:8080/file_update --notification-url-delete http://localhost:8080/file_delete

Note: The service automatically discovers monitored paths from .codemate/file_path.json
        """
    )

    # Main arguments
    parser.add_argument(
        "paths",
        nargs="*",
        help="Paths to monitor for file changes"
    )

    # Configuration options
    parser.add_argument(
        "--config", "-c",
        type=str,
        help="Path to configuration file (JSON)"
    )

    parser.add_argument(
        "--create-config",
        type=str,
        help="Create example configuration file"
    )

    parser.add_argument(
        "--recursive", "-r",
        action="store_true",
        default=True,
        help="Monitor directories recursively (default: True)"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Set logging level (default: INFO)"
    )

    parser.add_argument(
        "--log-file",
        type=str,
        help="Log to file instead of console"
    )

    parser.add_argument(
        "--debounce",
        type=float,
        default=2.0,
        help="Debounce interval in seconds (default: 2.0)"
    )

    parser.add_argument(
        "--include-ext",
        nargs="+",
        help="File extensions to include (e.g., .py .js .md)"
    )

    parser.add_argument(
        "--exclude-ext",
        nargs="+",
        help="File extensions to exclude"
    )

    parser.add_argument(
        "--max-size",
        type=int,
        default=10485760,  # 10MB
        help="Maximum file size to monitor in bytes (default: 10MB)"
    )

    parser.add_argument(
        "--notification-url-update",
        type=str,
        default="http://localhost:45312/watch_dog/file_update",
        help="URL for file update notifications (default: http://localhost:45312/watch_dog/file_update)"
    )

    parser.add_argument(
        "--notification-url-delete",
        type=str,
        default="http://localhost:45312/watch_dog/file_delete",
        help="URL for file delete notifications (default: http://localhost:45312/watch_dog/file_delete)"
    )

    parser.add_argument(
        "--web-port",
        type=int,
        default=45213,
        help="Web server port (default: 45213)"
    )

    parser.add_argument(
        "--no-web-server",
        action="store_true",
        help="Disable web server"
    )

    return parser


# Note: create_example_config and load_config functions are now imported from utils.py


def event_callback(event: FileChangeEvent) -> None:
    """Custom event callback for CLI with enhanced debug logging."""
    size_info = f" ({event.file_size} bytes)" if event.file_size else ""
    timestamp_str = datetime.fromtimestamp(event.timestamp).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

    LOGGER.info(f"📁 {event.event_type.value.upper()}: {event.relative_path or event.file_path}{size_info}")

    # Debug details
    LOGGER.debug(f"📁 File Event Details:")
    LOGGER.debug(f"📁 Event Type: {event.event_type.value}")
    LOGGER.debug(f"📁 File Path: {event.file_path}")
    LOGGER.debug(f"📁 Relative Path: {event.relative_path}")
    LOGGER.debug(f"📁 Timestamp: {timestamp_str}")
    LOGGER.debug(f"📁 File Size: {event.file_size} bytes" if event.file_size else "📁 File Size: Unknown")
    LOGGER.debug(f"📁 File Extension: {event.file_extension}" if event.file_extension else "📁 File Extension: None")

    if event.dest_path:
        LOGGER.debug(f"📁 Destination Path: {event.dest_path}")

    LOGGER.debug(f"📁 Event will trigger HTTP notification")


# ============================================================================
# MAIN APPLICATION
# ============================================================================

class StandaloneFileMonitor:
    """Main application class that coordinates all components."""

    def __init__(self, config: MonitorConfig):
        self.config = config
        self.monitor_service = FileMonitorService(config, event_callback)
        self.web_server = WebServer(config, self.monitor_service) if config.enable_web_server else None
        self.path_reloader = PathReloader(self.monitor_service, reload_interval=300.0)  # 5 minutes
        self._shutdown_requested = False

    async def start(self):
        """Start all services."""
        LOGGER.info("🚀 Starting Standalone File Monitor Service")

        # Start file monitoring
        await self.monitor_service.start()

        # Start web server if enabled
        if self.web_server:
            await self.web_server.start()

        # Start automatic path reloading
        await self.path_reloader.start()

        # Print startup information
        self._print_startup_info()

    async def stop(self):
        """Stop all services."""
        if self._shutdown_requested:
            return

        self._shutdown_requested = True
        LOGGER.info("🛑 Shutting down services...")

        # Stop path reloader
        await self.path_reloader.stop()

        # Stop web server
        if self.web_server:
            await self.web_server.stop()

        # Stop file monitoring
        await self.monitor_service.stop()

        LOGGER.info("✅ All services stopped")

    async def run_forever(self):
        """Run the application until shutdown."""
        try:
            await self.monitor_service.run_forever()
        except asyncio.CancelledError:
            LOGGER.info("Application cancelled")
        finally:
            await self.stop()

    def _print_startup_info(self):
        """Print startup information."""
        print(f"📂 Monitoring {len(self.config.monitored_paths)} path(s):")
        for path in self.config.monitored_paths:
            print(f"   • {path}")
        print(f"🔧 Recursive: {self.config.recursive}")
        print(f"⏱️  Debounce: {self.config.debounce_interval}s")
        print(f"� Auto-reload: Every 5 minutes from .codemate/file_path.json")
        print(f"�📝 Log level: {self.config.log_level}")
        print(f"🌐 Notification URLs:")
        print(f"   • File updates: {self.config.notification_urls['file_update']}")
        print(f"   • File deletes: {self.config.notification_urls['file_delete']}")

        if self.config.enable_web_server:
            print(f"🌐 Web server: http://{self.config.web_server_host}:{self.config.web_server_port}")

        if self.config.log_file:
            print(f"📄 Log file: {self.config.log_file}")

        print("\nPress Ctrl+C to stop monitoring...\n")


async def main() -> int:
    """Main application entry point."""
    parser = create_parser()
    args = parser.parse_args()

    # Handle config creation
    if args.create_config:
        create_example_config(args.create_config)
        return 0

    # Load configuration
    try:
        config = load_config(args)
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return 1

    # Validate paths
    valid_paths = []
    for path in config.monitored_paths:
        if Path(path).exists():
            valid_paths.append(str(Path(path).resolve()))
        else:
            print(f"⚠️  Path does not exist: {path}")

    if not valid_paths:
        print("❌ No valid paths to monitor")
        return 1

    config.monitored_paths = valid_paths

    # Create and start application
    app = StandaloneFileMonitor(config)

    # Setup signal handlers
    def signal_handler(signum, _):
        LOGGER.info(f"Received signal {signum}")
        asyncio.create_task(app.stop())

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        await app.start()
        await app.run_forever()
        return 0
    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested by user")
        return 0
    except Exception as e:
        LOGGER.error(f"❌ Application failed: {e}")
        return 1
    finally:
        await app.stop()

        # Print final statistics
        stats = app.monitor_service.get_status()
        print(f"\n📊 Final Statistics:")
        print(f"   • Uptime: {stats['uptime_seconds']:.1f} seconds")
        print(f"   • Events received: {stats['event_stats']['events_received']}")
        print(f"   • Events processed: {stats['event_stats']['events_processed']}")
        print(f"   • Events filtered: {stats['event_stats']['events_filtered']}")
        print(f"   • Notifications sent: {stats['notification_stats']['sent']}")
        print(f"   • Notifications failed: {stats['notification_stats']['failed']}")


def cli_main():
    """Entry point for CLI script."""
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


# ============================================================================
# EMBEDDED DEFAULT CONFIGURATION
# ============================================================================
# Note: DEFAULT_CONFIG is now available in utils.py


if __name__ == "__main__":
    cli_main()
